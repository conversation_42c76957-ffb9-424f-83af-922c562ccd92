exports.id=2095,exports.ids=[2095],exports.modules={17090:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("file-pen",[["path",{d:"M12.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v9.5",key:"1couwa"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1y4qbx"}]])},20174:(e,r,t)=>{"use strict";t.d(r,{F:()=>l});var s=t(60687),a=t(16189),i=t(85814),n=t.n(i);t(43210);let l=({items:e})=>{let r=(0,a.usePathname)(),t=e=>r.startsWith(e);return(0,s.jsx)("div",{className:"border-y border-neutral-400 rounded-md bg-primary-500 my-4 shadow-md",children:(0,s.jsx)("div",{className:"flex items-center",children:e.map(e=>e.disabled?(0,s.jsxs)("div",{className:"flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-gray-400 cursor-not-allowed",children:[e.icon,e.label]},e.route):(0,s.jsxs)(n(),{href:e.route,className:`flex items-center gap-2 font-medium transition-all duration-300 p-4 border-b-2 text-neutral-100 ${t(e.route)?"border-neutral-100":"border-transparent hover:border-neutral-400"}`,children:[e.icon,e.label]},e.route))})})}},28559:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},31207:(e,r,t)=>{"use strict";t.d(r,{XV:()=>a,cZ:()=>l,ru:()=>i,yi:()=>s});let s=(e,r)=>{let t=new Map;e.forEach(e=>{let s=r.filter(r=>r.questionGroupId===e.id).sort((e,r)=>e.position-r.position);t.set(e.id,{...e,subGroups:[],question:s})});let s=[];return e.forEach(e=>{let r=t.get(e.id);if(e.parentGroupId){let s=t.get(e.parentGroupId);s&&(s.subGroups=s.subGroups||[],s.subGroups.push(r))}else s.push(r)}),s},a=(e,r)=>{let t=[];return e.forEach(e=>{let r=e=>[...e.question||[],...(e.subGroups||[]).flatMap(r)],s=r(e),a=s.length>0?Math.min(...s.map(e=>e.position)):e.order;t.push({type:"group",data:e,order:a,originalPosition:a})}),r.forEach(e=>{t.push({type:"question",data:e,order:e.position,originalPosition:e.position})}),t.sort((e,r)=>e.order===r.order?(e.originalPosition||e.order)-(r.originalPosition||r.order):e.order-r.order)},i=e=>e.filter(e=>null===e.questionGroupId||void 0===e.questionGroupId),n=e=>{let r=[];return e.forEach(e=>{r.push(e.id),e.subGroups&&e.subGroups.length>0&&r.push(...n(e.subGroups))}),r},l=(e,r=!0)=>{let t={};return n(e).forEach(e=>{t[e]=r}),t}},31937:()=>{throw Error("Module parse failed: Identifier 'moveQuestionBetweenGroupsMutation' has already been declared (439:10)\nFile was processed with these loaders:\n * ./node_modules/next/dist/build/webpack/loaders/next-flight-client-module-loader.js\n * ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js\nYou may need an additional loader to handle the result of these loaders.\n|     });\n|     // Move question between groups mutation\n>     const moveQuestionBetweenGroupsMutation = useMutation({\n|         mutationFn: moveQuestionBetweenGroups,\n|         onSuccess: ()=>{")},38017:(e,r,t)=>{"use strict";t.d(r,{V:()=>N});var s=t(60687),a=t(43210),i=t.n(a),n=t(24934),l=t(39390),d=t(68988),o=t(15616),u=t(93437),c=t(40347),p=t(40228),h=t(48730),m=t(47033),x=t(11860),b=t(78272),v=t(14952),g=t(70332);t(24527);var j=t(69396),y=t(31207),f=t(77618);function N({questions:e,questionGroups:r=[],contextType:t="project",onClose:N,hashedId:q}){let[w,k]=(0,a.useState)({}),[A,G]=(0,a.useState)({}),[M,C]=(0,a.useState)([]),[I,R]=(0,a.useState)([]),[$,Q]=(0,a.useState)({}),E=(0,f.c3)(),P=i().useMemo(()=>(0,y.yi)(r,e),[r,e]),S=i().useMemo(()=>(0,y.ru)(e),[e]),F=i().useMemo(()=>"project"===t?(0,y.XV)(P,S):e.map(e=>({type:"question",data:e,order:e.position,originalPosition:e.position})),[P,S,e,t]),V=e=>{Q(r=>({...r,[e]:!r[e]}))},z=(e,r)=>{k(t=>({...t,[e]:r})),G(r=>({...r,[e]:""}))},T=e=>(0,s.jsxs)("div",{className:"border border-neutral-500 dark:border-neutral-700 rounded-md p-4",children:[(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsxs)(l.J,{className:"text-base font-medium",children:[e.label,e.isRequired&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),e.hint&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.hint}),A[e.id]&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:A[e.id]})]}),(0,s.jsx)("div",{className:"mt-2",children:B(e)})]},e.id),B=e=>{let r=w[e.id]??("selectmany"===e.inputType?[]:"");switch(e.inputType){case"text":if(e.hint?.includes("multiline"))return(0,s.jsx)(o.T,{value:r,onChange:r=>z(e.id,r.target.value),placeholder:e.hint||E("yourAnswer"),required:e.isRequired});return(0,s.jsx)(d.p,{value:r,onChange:r=>z(e.id,r.target.value),placeholder:e.hint||E("yourAnswer"),required:e.isRequired});case"number":return(0,s.jsx)(d.p,{type:"number",value:r,onChange:r=>z(e.id,r.target.value),placeholder:e.hint||E("yourAnswer"),required:e.isRequired});case"decimal":return(0,s.jsx)(d.p,{type:"number",step:"any",value:r,onChange:r=>z(e.id,r.target.value),placeholder:e.hint||E("yourAnswer"),required:e.isRequired});case"selectone":return(0,s.jsx)(c.z,{value:r,onValueChange:r=>z(e.id,r),required:e.isRequired,children:(0,s.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map((e,r)=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(c.C,{value:e.label,id:`option-${e.id}`}),(0,s.jsx)(l.J,{htmlFor:`option-${e.id}`,className:"cursor-pointer",children:e.label}),e.sublabel&&(0,s.jsx)("p",{className:"text-sm text-neutral-700 ml-4",children:`(${e.sublabel})`})]},r))})});case"selectmany":return(0,s.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map(t=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(u.S,{className:"w-5 h-5 border border-neutral-500",id:`option-${t.id}`,checked:(r||[]).includes(t.label),onCheckedChange:s=>{let a=r||[],i=s?[...a,t.label]:a.filter(e=>e!==t.label);z(e.id,i)}}),(0,s.jsxs)(l.J,{htmlFor:`option-${t.id}`,className:"cursor-pointer",children:[t.label," ",t.sublabel]})]},t.id))});case"date":return(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(d.p,{type:"date",value:r,onChange:r=>z(e.id,r.target.value),placeholder:e.hint||E("selectDate"),required:e.isRequired}),(0,s.jsx)(p.A,{className:"absolute top-3 right-3 h-4 w-4 text-muted-foreground pointer-events-none"})]});case"dateandtime":return(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(d.p,{type:"time",value:r,onChange:r=>z(e.id,r.target.value),placeholder:e.hint||E("selectTime"),required:e.isRequired}),(0,s.jsx)(h.A,{className:"absolute top-3 right-3 h-4 w-4 text-muted-foreground pointer-events-none"})]});case"table":return(0,s.jsx)(g.N,{questionId:e.id,value:r,onChange:r=>z(e.id,r),required:e.isRequired,tableLabel:e.label});default:return null}};return(0,s.jsxs)("div",{className:"bg-neutral-100 dark:bg-neutral-800 rounded-md shadow-sm border border-neutral-500 dark:border-neutral-700",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700",children:[(0,s.jsx)(n.$,{variant:"ghost",size:"icon",onClick:N,children:(0,s.jsx)(m.A,{className:"h-5 w-5"})}),(0,s.jsx)("h2",{className:"text-lg font-semibold",children:E("formPreview")}),(0,s.jsx)(n.$,{className:"cursor-pointer hover:bg-neutral-200",variant:"ghost",size:"icon",onClick:N,children:(0,s.jsx)(x.A,{className:"h-5 w-5"})})]}),(0,s.jsx)("div",{className:"p-4 md:p-6",children:(0,s.jsxs)("div",{className:"space-y-6",children:[F.map(r=>{if("group"===r.type){let t=r.data,a=$[t.id],i=e.filter(e=>e.questionGroupId===t.id),n=i.filter(e=>M.some(r=>r.id===e.id));return(0,s.jsxs)("div",{className:"border border-neutral-500 dark:border-neutral-600 rounded-lg bg-neutral-100 dark:bg-neutral-800 overflow-hidden",children:[(0,s.jsx)("div",{className:"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700 cursor-pointer hover:bg-neutral-200 dark:hover:bg-neutral-700",onClick:()=>V(t.id),children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[a?(0,s.jsx)(b.A,{className:"h-5 w-5 text-neutral-500"}):(0,s.jsx)(v.A,{className:"h-5 w-5 text-neutral-500"}),(0,s.jsx)("h3",{className:"text-lg font-semibold dark:text-neutral-100",children:t.title}),(0,s.jsxs)("span",{className:"text-sm text-neutral-700 dark:text-neutral-400",children:["(",n.length," ",E("visibleQuestion"),1!==n.length?E("s"):"",")"]})]})}),a&&(0,s.jsx)("div",{className:"p-4 space-y-4",children:I.filter(e=>i.some(r=>r.id===e.question.id)).map(e=>(0,s.jsx)(j.A,{questionGroup:e,renderQuestionInput:B,errors:A,className:""},e.question.id))})]},`group-${t.id}`)}{let e=r.data;if(!M.some(r=>r.id===e.id))return null;let t=I.find(r=>r.question.id===e.id);return t?(0,s.jsx)(j.A,{questionGroup:t,renderQuestionInput:B,errors:A,className:""},e.id):T(e)}}),0===e.length&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:E("noFormQuestionsYet")})}),e.length>0&&0===M.length&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:E("noVisibleQuestions")})})]})})]})}},40228:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},47033:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},48730:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])}};