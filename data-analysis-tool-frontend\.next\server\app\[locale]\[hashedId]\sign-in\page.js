(()=>{var e={};e.id=7254,e.ids=[7254],e.modules={3204:(e,s,t)=>{"use strict";t.d(s,{x:()=>u});var r=t(60687),a=t(43210),i=t(38587),n=t(19169),o=t(43649),l=t(12810),c=t(54864),d=t(19150),p=t(77618);let u=({email:e,showModal:s,setShowModal:t})=>{let u=(0,c.wA)(),m=(0,p.c3)(),x=async()=>{try{await l.A.post("/users/sendverificationemail",{email:e})}catch(e){u((0,d.Ds)({message:m("failedToSendVerification"),type:"error"}))}},[h,f]=(0,a.useState)(!0),[y,g]=(0,a.useState)(60);(0,a.useEffect)(()=>{let e;return h&&y>0?e=window.setInterval(()=>{g(e=>e-1)},1e3):0===y&&(f(!1),g(60)),()=>clearInterval(e)},[h,y]),(0,a.useEffect)(()=>(s&&e&&x(),()=>{g(60),f(!0)}),[s]);let b=async()=>{f(!0);try{await l.A.post("/users/sendverificationemail",{email:e})}catch(e){console.error("error sending email",e)}};return(0,r.jsxs)(i.A,{isOpen:s,onClose:()=>t(!1),className:"flex flex-col items-center gap-4",children:[(0,r.jsx)("div",{className:"rounded-full p-2 bg-primary-300",children:(0,r.jsx)(n.A,{className:"text-primary-500"})}),(0,r.jsx)("h1",{className:"heading-text",children:m("checkYourEmail")}),(0,r.jsxs)("p",{className:"flex flex-col items-center",children:[m("verificationSentTo"),(0,r.jsx)("span",{className:"font-medium",children:e})]}),(0,r.jsxs)("div",{className:"flex gap-2 items-center bg-yellow-100 text-yellow-900 px-4 py-2 rounded-md",children:[(0,r.jsx)(o.A,{size:16})," ",m("didNotReceiveEmail")]}),(0,r.jsx)("button",{className:"btn-primary",onClick:b,disabled:h,children:h?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("span",{children:[m("resendIn")," ",y,"s"]}),(0,r.jsx)("div",{className:"size-4 animate-spin border-x-2 rounded-full"})]}):(0,r.jsx)("span",{children:m("resend")})})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19169:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},21820:e=>{"use strict";e.exports=require("os")},25529:(e,s,t)=>{Promise.resolve().then(t.bind(t,85101))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36039:(e,s,t)=>{Promise.resolve().then(t.bind(t,45196))},38587:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var r=t(60687),a=t(88920),i=t(57101),n=t(74699),o=t(11860);t(43210);let l=({children:e,className:s,isOpen:t,onClose:l,preventOutsideClick:c=!1})=>(0,r.jsx)(a.N,{children:t&&(0,r.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto",onClick:e=>{c||l()},children:(0,r.jsxs)(i.P.div,{initial:{scale:.6,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.6,opacity:0},transition:{duration:.3,ease:n.am},className:`relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ${s}`,onClick:e=>e.stopPropagation(),children:[(0,r.jsx)(o.A,{onClick:l,className:"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"}),e]})})})},38935:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var r=t(60687),a=t(3204),i=t(19150),n=t(63442),o=t(38038),l=t(12597),c=t(13861),d=t(16189),p=t(43210),u=t(27605),m=t(54864),x=t(45880),h=t(21650);let f=x.z.object({email:x.z.string().min(1,"Email is required").email("Please enter a valid email address"),password:x.z.string().min(1,"Password is required")}),y=()=>{let{register:e,formState:{errors:s,isSubmitting:t},handleSubmit:x,getValues:y,watch:g}=(0,u.mN)({resolver:(0,n.u)(f)}),b=g("password"),v=(0,d.useRouter)(),j=(0,m.wA)(),{hashedId:w}=(0,d.useParams)(),N=`/form-test/${w}`,[k,P]=(0,p.useState)(!1),[A,E]=(0,p.useState)(!1),{signin:q}=(0,h.A)({skipFetchUser:!0}),C=async e=>{q({email:e.email,password:e.password},()=>{j((0,i.Ds)({message:"Sign in successful.",type:"success"})),v.push(N)},e=>{"unverified"===e?P(!0):j((0,i.Ds)({message:"Invalid email or password. Please try again.",type:"error"}))})};return(0,r.jsxs)("div",{className:"min-h-screen flex items-center justify-center",children:[(0,r.jsx)(a.x,{email:y("email"),showModal:k,setShowModal:P}),(0,r.jsxs)("div",{className:"flex flex-col section w-11/12 mobile:w-4/5 tablet:w-lg",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2 mb-8",children:[(0,r.jsx)(o.A,{size:36}),(0,r.jsx)("h1",{className:"text-2xl tablet:text-3xl font-semibold text-center",children:"Sign in to access this form"}),(0,r.jsx)("p",{className:"text-neutral-700 text-center",children:"Log in to complete the data analysis form"})]}),(0,r.jsxs)("form",{className:"flex flex-col gap-4 mb-4",onSubmit:x(C),children:[(0,r.jsxs)("div",{className:"group label-input-group",children:[(0,r.jsx)("label",{htmlFor:"email",className:"label-text",children:"Email"}),(0,r.jsx)("input",{...e("email"),id:"email",type:"email",placeholder:"Enter your email address",className:"input-field"}),s.email&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${s.email.message}`})]}),(0,r.jsxs)("div",{className:"group label-input-group",children:[(0,r.jsx)("label",{htmlFor:"password",className:"label-text",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{...e("password"),id:"password",type:A?"text":"password",placeholder:"Enter your password",className:"input-field w-full pr-10"}),b&&b.length>0&&(0,r.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>E(!A),children:[A?(0,r.jsx)(l.A,{className:"h-4 w-4"}):(0,r.jsx)(c.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{className:"sr-only",children:[A?"Hide":"Show"," password"]})]})]}),s.password&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${s.password.message}`})]}),(0,r.jsx)("button",{type:"submit",className:"btn-primary",disabled:t,children:t?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:["Signing in"," ",(0,r.jsx)("div",{className:"size-4 rounded-full border-x-2 animate-spin"})]}):"Submit"})]})]})]})}},52911:(e,s,t)=>{Promise.resolve().then(t.bind(t,80994))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61040:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let c={children:["",{children:["[locale]",{children:["[hashedId]",{children:["sign-in",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,85101)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\[hashedId]\\sign-in\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\[hashedId]\\sign-in\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/[hashedId]/sign-in/page",pathname:"/[locale]/[hashedId]/sign-in",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},61977:(e,s,t)=>{Promise.resolve().then(t.bind(t,38935))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72121:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c,generateStaticParams:()=>l});var r=t(37413),a=t(60958),i=t(39916),n=t(81015);let o=["en","ne"];function l(){return o.map(e=>({locale:e}))}async function c({children:e,params:s}){let{locale:t}=await s;o.includes(t)||(0,i.notFound)();let l=await (0,n.V)(t);return(0,r.jsx)(a.A,{locale:t,messages:l,children:e})}},74075:e=>{"use strict";e.exports=require("zlib")},76565:(e,s,t)=>{var r={"./en.json":[87368,7368],"./ne.json":[3018,3018]};function a(e){if(!t.o(r,e))return Promise.resolve().then(()=>{var s=Error("Cannot find module '"+e+"'");throw s.code="MODULE_NOT_FOUND",s});var s=r[e],a=s[0];return t.e(s[1]).then(()=>t.t(a,19))}a.keys=()=>Object.keys(r),a.id=76565,e.exports=a},79551:e=>{"use strict";e.exports=require("url")},81015:(e,s,t)=>{"use strict";t.d(s,{A:()=>n,V:()=>i});var r=t(35471);let a=["en","ne"];async function i(e){a.includes(e)||(console.warn(`Unsupported locale: ${e}, falling back to 'en'`),e="en");try{let s=(await t(76565)(`./${e}.json`)).default;if(!s||"object"!=typeof s)throw Error(`Invalid messages format for locale: ${e}`);return s}catch(s){if(console.error(`Failed to load messages for locale: ${e}`,s),"en"!==e)try{return console.log("Falling back to English messages"),(await t.e(7368).then(t.t.bind(t,87368,19))).default}catch(e){console.error("Failed to load fallback English messages",e)}return{}}}let n=(0,r.A)(async({locale:e})=>{let s=e?.toString()||"en";return{locale:s,messages:await i(s),timeZone:"Asia/Kathmandu",formats:{dateTime:{short:{day:"numeric",month:"short",year:"numeric"},medium:{day:"numeric",month:"long",year:"numeric"},long:{weekday:"long",day:"numeric",month:"long",year:"numeric"}},number:{currency:{style:"currency",currency:"NPR"}}}}})},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85101:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\[hashedId]\\\\sign-in\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\[hashedId]\\sign-in\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,7404,1658,6560,8610,5374,2198,5823,5841],()=>t(61040));module.exports=r})();