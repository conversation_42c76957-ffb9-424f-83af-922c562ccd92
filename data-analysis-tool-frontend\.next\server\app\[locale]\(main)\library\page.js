(()=>{var e={};e.id=9454,e.ids=[9454],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7053:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(main)\\\\library\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\library\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21324:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=s(65239),a=s(48088),l=s(88170),n=s.n(l),i=s(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d={children:["",{children:["[locale]",{children:["(main)",{children:["library",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,7053)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\library\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,84606)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(main)\\library\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/(main)/library/page",pathname:"/[locale]/library",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},21820:e=>{"use strict";e.exports=require("os")},24119:(e,t,s)=>{Promise.resolve().then(s.bind(s,76352))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73678:(e,t,s)=>{"use strict";s.d(t,{R:()=>l});var r=s(60687);s(43210);var a=s(38587);let l=({showModal:e,onClose:t,onConfirm:s,title:l,description:n,confirmButtonText:i,cancelButtonText:o,confirmButtonClass:d,children:c})=>(0,r.jsxs)(a.A,{isOpen:e,onClose:t,className:"p-6 rounded-md max-w-xl",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-700",children:l}),(0,r.jsx)("div",{className:"text-neutral-700 mt-2",children:n}),c&&(0,r.jsx)("div",{className:"mt-6 space-y-4",children:c}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 mt-6",children:[(0,r.jsx)("button",{className:"btn-outline",onClick:t,type:"button",children:o||"Cancel"}),(0,r.jsx)("button",{className:`font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${d}`,onClick:s,type:"button",children:i})]})]})},74075:e=>{"use strict";e.exports=require("zlib")},75531:(e,t,s)=>{"use strict";s.d(t,{K4:()=>a,dI:()=>n,ej:()=>l});var r=s(12810);let a=async({projectId:e})=>{let{data:t}=await r.A.get(`/questions/${e}`);return t.questions},l=async({templateId:e})=>{let{data:t}=await r.A.get(`/template-questions/${e}`);return t.questions},n=async()=>{try{return(await r.A.get("/question-blocks")).data.questions||[]}catch(e){throw console.error("Error fetching question block questions:",e),e}}},76352:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>_});var r=s(60687),a=s(21650),l=s(3984),n=s(29494),i=s(8693),o=s(54050),d=s(43210),c=s.n(d),u=s(86429),p=s(73678),m=s(93617),h=s(78272),x=s(3589);let g=(0,s(62688).A)("trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]]);var b=s(55629),y=s(26312),j=s(43782),f=s(6986),v=s(85814),N=s.n(v);let C=[{id:"select",header:({table:e})=>(0,r.jsx)(j.Sc,{className:"w-6 h-6 data-[state=checked]:bg-primary-500 data-[state=checked]:text-neutral-500 rounded border border-neutral-100 data-[state=checked]:border-neutral-100 cursor-pointer",checked:e.getIsAllPageRowsSelected()||e.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:t=>e.toggleAllPageRowsSelected(!!t),"aria-label":"Select all"}),cell:({row:e})=>(0,r.jsx)(j.Sc,{className:"w-6 h-6 bg-neutral-100 rounded border border-neutral-400 data-[state=checked]:bg-neutral-100 data-[state=checked]:text-primary-500 data-[state=checked]:border-primary-500 cursor-pointer",checked:e.getIsSelected(),onCheckedChange:t=>e.toggleSelected(!!t),"aria-label":"Select row"}),enableHiding:!1,enableSorting:!1},{accessorKey:"name",header:"Template Name",cell:({row:e})=>{let t=e.original.id,s=(0,f.l)(t);return(0,r.jsx)(N(),{href:`library/template/${s}/form-builder`,className:"cursor-pointer text-primary-500 hover:text-primary-600 hover:underline transition-all duration-300",children:e.getValue("name")})}},{accessorKey:"description",header:"Description"},{id:"owner",accessorFn:e=>e.user?.name??"unknown",header:"Owner",cell:({getValue:e})=>e()},{accessorKey:"sector",header:"Sector"},{accessorKey:"country",header:"Country"},{accessorKey:"updatedAt",header:"Last Modified"}];var S=s(77618);let w="data-table-column-visibility",k=()=>{let{user:e}=(0,a.A)(),t=(0,S.c3)(),[s,j]=(0,d.useState)(!1),{data:f,isLoading:v,isError:N}=(0,n.I)({queryKey:["templates",e?.id],queryFn:l.QK,enabled:!!e?.id}),[k,q]=(0,d.useState)(""),[P,A]=(0,d.useState)({}),[K,M]=(0,d.useState)(null),[R,E]=(0,d.useState)(!1),[O,_]=(0,d.useState)({}),I=(0,i.jE)();(0,d.useEffect)(()=>{let e=localStorage.getItem(w);if(e)try{A(JSON.parse(e))}catch(e){console.error("Failed to parse saved column visibility",e)}},[]),(0,d.useEffect)(()=>{Object.keys(P).length>0&&localStorage.setItem(w,JSON.stringify(P))},[P]);let F=(0,o.n)({mutationFn:l.nh,onSuccess:()=>{I.invalidateQueries({queryKey:["templates",e?.id]}),j(!1)},onError:()=>{}}),V=c().useMemo(()=>Object.keys(O).filter(e=>O[e]).map(e=>parseInt(e,10)),[O]);return v||!f?(0,r.jsx)(u.A,{}):N?(0,r.jsx)("p",{className:"text-red-500",children:t("error_loading_data")}):(0,r.jsxs)("div",{children:[(0,r.jsx)(p.R,{showModal:s,onClose:()=>j(!1),title:t("deleteTemplates"),description:t("confirmDeleteTemplates"),confirmButtonText:t("delete"),confirmButtonClass:"btn-danger",onConfirm:()=>F.mutate({templateIds:V})}),(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("h1",{className:"sub-heading-text",children:t("templates")}),(0,r.jsx)("div",{children:(0,r.jsx)("input",{type:"text",value:k,onChange:e=>q(e.target.value),placeholder:t("searchTemplates"),className:"input-field text-sm"})}),K&&(0,r.jsxs)(b.rI,{open:R,onOpenChange:e=>E(e),children:[(0,r.jsx)(y.ty,{asChild:!0,children:(0,r.jsxs)("button",{className:"btn-outline text-sm text-neutral-700 border-neutral-400 font-normal",children:[t("showHideColumns"),R?(0,r.jsx)(h.A,{size:16}):(0,r.jsx)(x.A,{size:16})]})}),(0,r.jsx)(b.SQ,{align:"start",className:"border bg-neutral-100 border-neutral-200 shadow-md px-2",children:K.getAllColumns().filter(e=>e.getCanHide()).map(e=>(0,r.jsx)(b.hO,{className:"capitalize cursor-pointer hover:bg-neutral-200",checked:P[e.id]??!0,onCheckedChange:t=>A(s=>({...s,[e.id]:t})),children:e.id},e.id))})]}),(0,r.jsxs)("button",{type:"button",className:"ml-auto btn-danger text-sm",onClick:()=>{0!==V.length&&j(!0)},disabled:0===V.length,children:[t("delete")," ",(0,r.jsx)(g,{size:16})]})]}),f.length>0?(0,r.jsx)(m.x,{columns:C,data:f,globalFilter:k,setGlobalFilter:q,onTableInit:e=>M(e),onRowSelectionChange:_,columnVisibility:P,setColumnVisibility:A}):(0,r.jsxs)("div",{className:"text-center py-16 space-y-4",children:[(0,r.jsx)("p",{className:"text-lg",children:t("getStarted")}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:t("advancedUsers")})]})]})]})},q=()=>{let e=(0,S.c3)();return[{id:"sno",header:e("sn"),cell:({row:e})=>e.index+1},{accessorKey:"label",header:e("question")},{accessorKey:"inputType",header:e("inputType"),cell:({row:e})=>{let t=e.getValue("inputType");return t.charAt(0).toUpperCase()+t.slice(1)}},{accessorKey:"questionOptions",header:e("options"),cell:({row:e})=>{let t=e.original.inputType;if("selectone"===t||"selectmany"===t){let t=e.original.questionOptions||[];return(0,r.jsx)("div",{className:"space-y-1",children:t.map((e,t)=>(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsxs)("span",{className:"font-medium",children:["-",e.label]}),e.sublabel&&(0,r.jsxs)("span",{className:"text-neutral-500 ml-2",children:["(",e.sublabel,")"]})]},t))})}return null}},{accessorKey:"hint",header:e("hint")},{accessorKey:"isRequired",header:e("required"),cell:({row:e})=>e.getValue("isRequired")?"Yes":"No"},{accessorKey:"updatedAt",header:e("lastModified"),cell:({row:e})=>new Date(e.getValue("updatedAt")).toLocaleDateString()}]};var P=s(56090),A=s(93772),K=s(96752);let M=({columns:e,data:t,globalFilter:s,setGlobalFilter:a,onTableInit:l,columnVisibility:n,setColumnVisibility:i,onRowSelectionChange:o,rowSelection:d,onRowClick:u})=>{let[p,m]=c().useState({pageIndex:0,pageSize:8}),[h,x]=c().useState([]),[g,b]=c().useState([]),[y,j]=c().useState({}),[f,v]=c().useState({}),N=void 0!==d?d:f,C=(0,P.N4)({data:t,columns:e,onPaginationChange:m,onColumnFiltersChange:x,onGlobalFilterChange:a,onColumnVisibilityChange:i??j,onRowSelectionChange:e=>{let t="function"==typeof e?e(N):e;void 0===d&&v(t),o&&o(t)},onSortingChange:b,getCoreRowModel:(0,A.HT)(),getFilteredRowModel:(0,A.hM)(),getPaginationRowModel:(0,A.kW)(),getSortedRowModel:(0,A.h5)(),enableRowSelection:!0,enableSorting:!0,enableSortingRemoval:!0,state:{pagination:p,columnFilters:h,globalFilter:s,columnVisibility:n??y,rowSelection:N,sorting:g}}),w=(0,S.c3)();return c().useEffect(()=>{l&&l(C)},[l,C]),c().useEffect(()=>{void 0!==d&&C.setRowSelection(d)},[d,C]),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"rounded-md border border-neutral-400 overflow-hidden",children:(0,r.jsxs)(K.XI,{className:"min-w-full",children:[(0,r.jsx)(K.A0,{className:"h-20",children:C.getHeaderGroups().map(e=>(0,r.jsx)(K.Hj,{className:"text-sm border-neutral-400",children:e.headers.map(e=>(0,r.jsxs)(K.nd,{className:`py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold ${0===e.index?"w-12 py-3 px-6":""}`,style:{cursor:e.column.getCanSort()?"pointer":"default"},children:[(0,r.jsx)("div",{onClick:e.column.getToggleSortingHandler(),children:(0,r.jsx)("div",{children:e.isPlaceholder?null:(0,P.Kv)(e.column.columnDef.header,e.getContext())})}),e.column.getCanFilter()&&(0,r.jsx)("input",{placeholder:w("search"),value:e.column.getFilterValue()||"",onChange:t=>e.column.setFilterValue(t.target.value),className:"input-field max-w-48 text-sm my-1 px-2 py-1 bg-neutral-100 text-neutral-700 font-light border-none rounded-md"})]},e.id))},e.id))}),(0,r.jsx)(K.BF,{children:C.getPaginationRowModel().rows.length?C.getPaginationRowModel().rows.map(e=>(0,r.jsx)(K.Hj,{"data-state":e.getIsSelected()&&"selected",className:"hover:bg-neutral-50 text-sm border-neutral-400",onClick:()=>u?.(e.original),children:e.getVisibleCells().map((e,t)=>(0,r.jsx)(K.nA,{className:`py-4 px-6 max-w-48  ${0===t?"py-3 px-6":""} text-neutral-700 `,children:(0,P.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,r.jsx)(K.Hj,{children:(0,r.jsx)(K.nA,{colSpan:e.length,className:"h-24 text-center",children:w("no_results")})})})]})}),(0,r.jsx)("div",{className:"flex items-center justify-end space-x-2 py-4",children:t.length>p.pageSize&&(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,r.jsx)("button",{className:"btn-primary",onClick:()=>C.previousPage(),disabled:!C.getCanPreviousPage(),children:w("previous")}),(0,r.jsx)("button",{className:"btn-primary",onClick:()=>C.nextPage(),disabled:!C.getCanNextPage(),children:w("next")})]})})]})},R=({questions:e})=>{let[t,s]=(0,d.useState)(""),a=q();return(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsx)(M,{columns:a,data:e,globalFilter:t,setGlobalFilter:s})})};var E=s(75531);let O=()=>{let{user:e}=(0,a.A)(),t=e?.id,{data:s,isLoading:r,isError:l,error:i,refetch:o}=(0,n.I)({queryKey:["questionBlockQuestions",t],queryFn:()=>(0,E.dI)(),enabled:!!t,retry:1});return{questions:Array.isArray(s)?s:[],isLoading:r,isError:l,error:i,refetch:o,userId:t}};function _(){let{questions:e}=O(),t=(0,S.c3)();return(0,r.jsxs)("div",{className:"p-6 space-y-6 section gap-8",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold",children:t("myLibrary")}),(0,r.jsx)(k,{}),(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsx)("h1",{className:"sub-heading-text hover:text-neutral-700",children:(0,r.jsx)(N(),{href:"/library/question-block/form-builder",children:t("questionBlocks")})}),e.length>0?(0,r.jsx)(R,{questions:e}):(0,r.jsxs)("div",{className:"text-center py-16 space-y-4",children:[(0,r.jsx)("p",{className:"text-lg",children:t("getStarted")}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:t("advancedUsers")})]})]})]})}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87167:(e,t,s)=>{Promise.resolve().then(s.bind(s,7053))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7404,1658,6560,8610,5374,2198,5814,3851,8581,5841,5041],()=>s(21324));module.exports=r})();