(()=>{var e={};e.id=1968,e.ids=[1968],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10125:(e,t,a)=>{"use strict";a.d(t,{Notification:()=>m});var r=a(60687),s=a(43210),i=a(54864),n=a(88920),o=a(57101),l=a(19150),c=a(14719),d=a(43649),u=a(93613);let m=()=>{let e=(0,i.wA)(),{message:t,type:a,visible:m}=(0,i.d4)(e=>e.notification);(0,s.useEffect)(()=>{if(m){let t=setTimeout(()=>{e((0,l._b)())},5e3);return()=>clearTimeout(t)}},[m,e]);let p="success"===a?(0,r.jsx)(c.A,{}):"warning"===a?(0,r.jsx)(d.A,{}):(0,r.jsx)(u.A,{});return(0,r.jsx)(n.N,{children:m&&(0,r.jsxs)(o.P.div,{className:`z-50 fixed top-0 right-0 m-4 px-4 py-2 rounded font-semibold w-auto max-w-xs flex items-center gap-2 cursor-pointer ${"success"===a?"bg-green-500 hover:bg-green-600":"warning"===a?"bg-yellow-500 hover:bg-yellow-600":"bg-red-500 hover:bg-red-600"} transition-colors duration-300`,onClick:()=>e((0,l._b)()),initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3,ease:"easeIn"},children:[(0,r.jsx)("span",{className:"text-2xl",children:p}),(0,r.jsx)("span",{className:"break-words neutral-100space-normal",children:t})]})})}},10271:(e,t,a)=>{"use strict";a.d(t,{ReactQueryProvider:()=>l});var r=a(60687),s=a(43210),i=a(39091),n=a(8693),o=a(9124);let l=({children:e})=>{let[t]=(0,s.useState)(()=>new i.E({defaultOptions:{queries:{staleTime:3e5,refetchOnWindowFocus:!1}}}));return(0,r.jsxs)(n.Ht,{client:t,children:[e,(0,r.jsx)(o.E,{initialIsOpen:!1})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11437:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},12412:e=>{"use strict";e.exports=require("assert")},12810:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let r=a(51060).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let s=r},15566:e=>{"use strict";e.exports=JSON.parse('["Afghanistan","Albania","Algeria","Andorra","Angola","Argentina","Armenia","Australia","Austria","Azerbaijan","Bahamas","Bahrain","Bangladesh","Barbados","Belarus","Belgium","Belize","Benin","Bhutan","Bolivia","Bosnia and Herzegovina","Botswana","Brazil","Brunei","Bulgaria","Burkina Faso","Burundi","Cabo Verde","Cambodia","Cameroon","Canada","Central African Republic","Chad","Chile","China","Colombia","Comoros","Congo (Congo-Brazzaville)","Costa Rica","Croatia","Cuba","Cyprus","Czech Republic","Denmark","Djibouti","Dominica","Dominican Republic","Ecuador","Egypt","El Salvador","Equatorial Guinea","Eritrea","Estonia","Eswatini","Ethiopia","Fiji","Finland","France","Gabon","Gambia","Georgia","Germany","Ghana","Greece","Grenada","Guatemala","Guinea","Guinea-Bissau","Guyana","Haiti","Honduras","Hungary","Iceland","India","Indonesia","Iran","Iraq","Ireland","Israel","Italy","Jamaica","Japan","Jordan","Kazakhstan","Kenya","Kiribati","Kuwait","Kyrgyzstan","Laos","Latvia","Lebanon","Lesotho","Liberia","Libya","Liechtenstein","Lithuania","Luxembourg","Madagascar","Malawi","Malaysia","Maldives","Mali","Malta","Marshall Islands","Mauritania","Mauritius","Mexico","Micronesia","Moldova","Monaco","Mongolia","Montenegro","Morocco","Mozambique","Myanmar","Namibia","Nauru","Nepal","Netherlands","New Zealand","Nicaragua","Niger","Nigeria","North Korea","North Macedonia","Norway","Oman","Pakistan","Palau","Palestine","Panama","Papua New Guinea","Paraguay","Peru","Philippines","Poland","Portugal","Qatar","Romania","Russia","Rwanda","Saint Kitts and Nevis","Saint Lucia","Saint Vincent and the Grenadines","Samoa","San Marino","Sao Tome and Principe","Saudi Arabia","Senegal","Serbia","Seychelles","Sierra Leone","Singapore","Slovakia","Slovenia","Solomon Islands","Somalia","South Africa","South Korea","South Sudan","Spain","Sri Lanka","Sudan","Suriname","Sweden","Switzerland","Syria","Taiwan","Tajikistan","Tanzania","Thailand","Timor-Leste","Togo","Tonga","Trinidad and Tobago","Tunisia","Turkey","Turkmenistan","Tuvalu","Uganda","Ukraine","United Arab Emirates","United Kingdom","United States","Uruguay","Uzbekistan","Vanuatu","Vatican City","Venezuela","Vietnam","Yemen","Zambia","Zimbabwe"]')},16319:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,86346,23)),Promise.resolve().then(a.t.bind(a,27924,23)),Promise.resolve().then(a.t.bind(a,35656,23)),Promise.resolve().then(a.t.bind(a,40099,23)),Promise.resolve().then(a.t.bind(a,38243,23)),Promise.resolve().then(a.t.bind(a,28827,23)),Promise.resolve().then(a.t.bind(a,62763,23)),Promise.resolve().then(a.t.bind(a,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19150:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>n,Ds:()=>s,_b:()=>i});let r=(0,a(9317).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,t)=>{e.message=t.payload.message,e.type=t.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:s,hideNotification:i}=r.actions,n=r.reducer},21820:e=>{"use strict";e.exports=require("os")},25031:(e,t,a)=>{Promise.resolve().then(a.bind(a,76509))},26946:(e,t,a)=>{Promise.resolve().then(a.bind(a,10125)),Promise.resolve().then(a.bind(a,10271)),Promise.resolve().then(a.bind(a,49271))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32833:(e,t,a)=>{"use strict";a.d(t,{b:()=>r});let r={information_media:"Information / Media",econommic_social_development:"Economic & Social Development",security_police_peacekeeping:"Security / Police / Peacekeeping",disarmament_and_demobilization:"Disarmament & Demobilization",environment:"Environment",private_sector:"Private Sector",humanitarian_coordination_information_management:"Humanitarian - Coordination & Info Management",humanitarian_multiple_clusters:"Humanitarian - Multiple Clusters",humanitarian_camp_management_and_coordination:"Humanitarian - Camp Management & Coordination",humanitarian_early_recovery:"Humanitarian - Early Recovery",humanitarian_education:"Humanitarian - Education",humanitarian_emergency_shelter:"Humanitarian - Emergency Shelter",humanitarian_emergency_telecoms:"Humanitarian - Emergency Telecoms",humanitarian_food_security:"Humanitarian - Food Security",humanitarian_health:"Humanitarian - Health",humanitarian_logistics:"Humanitarian - Logistics",humanitarian_nutrition:"Humanitarian - Nutrition",humanitarian_protection:"Humanitarian - Protection",humanitarian_sanitation_water_and_hygiene:"Humanitarian - Sanitation / Water / Hygiene",other:"Other"}},33873:e=>{"use strict";e.exports=require("path")},35790:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>n,l:()=>i,yg:()=>s});let r=(0,a(9317).Z0)({name:"createLibraryItem",initialState:{visible:!1},reducers:{showCreateLibraryModal:e=>{e.visible=!0},hideCreateLibraryModal:e=>{e.visible=!1}}}),{showCreateLibraryModal:s,hideCreateLibraryModal:i}=r.actions,n=r.reducer},36039:(e,t,a)=>{Promise.resolve().then(a.bind(a,45196))},40480:(e,t,a)=>{"use strict";a.d(t,{H:()=>r});let r=(e,t)=>{let a=Object.entries(t).find(([t,a])=>a===e);return a?a[0]:null}},42895:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>l,Le:()=>n,jB:()=>o,tQ:()=>s,x9:()=>i});let r=(0,a(9317).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:s,setUnauthenticated:i,setAuthLoading:n,setAuthError:o}=r.actions,l=r.reducer},44395:(e,t,a)=>{"use strict";a.d(t,{Notification:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call Notification() from the server but Notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\components\\general\\Notification.tsx","Notification")},46055:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var r=a(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},49271:(e,t,a)=>{"use strict";a.d(t,{ReduxProvider:()=>m});var r=a(60687),s=a(9317),i=a(19150),n=a(58432),o=a(42895),l=a(35790),c=a(89011);let d=(0,s.U1)({reducer:{notification:i.Ay,createProject:n.Ay,auth:o.Ay,createLibrary:l.Ay,createLibraryItem:c.Ay}});a(43210);var u=a(54864);let m=({children:e})=>(0,r.jsx)(u.Kq,{store:d,children:e})},50823:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,16444,23)),Promise.resolve().then(a.t.bind(a,16042,23)),Promise.resolve().then(a.t.bind(a,88170,23)),Promise.resolve().then(a.t.bind(a,49477,23)),Promise.resolve().then(a.t.bind(a,29345,23)),Promise.resolve().then(a.t.bind(a,12089,23)),Promise.resolve().then(a.t.bind(a,46577,23)),Promise.resolve().then(a.t.bind(a,31307,23))},52911:(e,t,a)=>{Promise.resolve().then(a.bind(a,80994))},54306:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=a(65239),s=a(48088),i=a(88170),n=a.n(i),o=a(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(t,l);let c={children:["",{children:["[locale]",{children:["(auth)",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,93091)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(auth)\\signup\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,72121)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(auth)\\signup\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/[locale]/(auth)/signup/page",pathname:"/[locale]/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57800:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},58014:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m,metadata:()=>u});var r=a(37413);a(82704);var s=a(7990),i=a.n(s),n=a(60866),o=a.n(n),l=a(77832),c=a(44395),d=a(60265);let u={title:"Data analysis tool",description:"A tool for data collection and analysis."};function m({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${i().className} ${o().className} antialiased`,children:(0,r.jsx)(l.ReduxProvider,{children:(0,r.jsxs)(d.ReactQueryProvider,{children:[(0,r.jsx)(c.Notification,{}),(0,r.jsx)("main",{className:"bg-neutral-200",children:e})]})})})})}},58432:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>n,Gl:()=>s,th:()=>i});let r=(0,a(9317).Z0)({name:"createProject",initialState:{visible:!1},reducers:{showCreateProjectModal:e=>{e.visible=!0},hideCreateProjectModal:e=>{e.visible=!1}}}),{showCreateProjectModal:s,hideCreateProjectModal:i}=r.actions,n=r.reducer},60265:(e,t,a)=>{"use strict";a.d(t,{ReactQueryProvider:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReactQueryProvider.tsx","ReactQueryProvider")},62163:(e,t,a)=>{"use strict";a.d(t,{i:()=>r});let r={non_profit_organization:"Non-profit Organization",government_institution:"Government Institution",educational_organization:"Educational Organization",a_commercial_or_for_profit_company:"Commercial / For-profit Company",i_am_not_associated_with_any_organization:"Not Associated with any Organization"}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64668:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(60687),s=a(8610),i=a(16189),n=a(85814),o=a.n(n);function l(){let e=(0,s.Ym)(),t=(0,i.usePathname)(),a=e=>{let a=t.replace(/^\/(en|ne)/,"");return`/${e}${a}`};return(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(o(),{href:a("en"),className:`px-3 py-1 rounded ${"en"===e?"bg-primary-600 text-white":"bg-gray-200"}`,children:"English"}),(0,r.jsx)(o(),{href:a("ne"),className:`px-3 py-1 rounded ${"ne"===e?"bg-primary-600 text-white":"bg-gray-200"}`,children:"नेपाली"})]})}},65199:(e,t,a)=>{Promise.resolve().then(a.bind(a,93091))},68292:(e,t,a)=>{"use strict";a.d(t,{l:()=>n});var r=a(60687),s=a(78272),i=a(43210);let n=({id:e,options:t,value:a,onChange:n})=>{let[o,l]=(0,i.useState)(!1),c=(0,i.useRef)(null),d=(0,i.useRef)([]),u=(0,i.useRef)(null);(0,i.useEffect)(()=>{let e=e=>{u.current&&!u.current.contains(e.target)&&l(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let m=e=>{if(!o)return;let a=e.key.toLowerCase();if(a.match(/[a-z]/)){let e=t.findIndex(e=>e.toLowerCase().startsWith(a));-1!==e&&d.current[e]&&d.current[e]?.scrollIntoView({behavior:"auto",block:"nearest"})}};return(0,i.useEffect)(()=>(document.addEventListener("keydown",m),()=>{document.removeEventListener("keydown",m)}),[o,t]),(0,r.jsxs)("div",{className:"relative",ref:u,children:[(0,r.jsxs)("button",{id:e,type:"button",className:"px-4 py-2 flex items-center justify-between rounded-md border border-neutral-400 focus:border-primary-500 duration-300 w-full text-left cursor-pointer",onClick:()=>{l(!o)},children:[(0,r.jsx)("span",{children:a||"Select an option"}),(0,r.jsx)(s.A,{})]}),o&&(0,r.jsx)("ul",{className:"absolute z-10 max-h-[180px] overflow-auto border border-neutral-400 rounded-md bg-neutral-100 w-full mt-1 flex flex-col",ref:c,children:t.map((e,t)=>(0,r.jsx)("li",{ref:e=>{d.current[t]=e},className:"cursor-pointer bg-neutral-100 hover:bg-neutral-200 px-4 py-2",onClick:()=>{n(e),l(!1)},children:e},t))})]})}},72121:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c,generateStaticParams:()=>l});var r=a(37413),s=a(60958),i=a(39916),n=a(81015);let o=["en","ne"];function l(){return o.map(e=>({locale:e}))}async function c({children:e,params:t}){let{locale:a}=await t;o.includes(a)||(0,i.notFound)();let l=await (0,n.V)(a);return(0,r.jsx)(s.A,{locale:a,messages:l,children:e})}},74075:e=>{"use strict";e.exports=require("zlib")},76509:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>S});var r=a(60687),s=a(63442),i=a(38038),n=a(12597),o=a(13861),l=a(11437),c=a(57800),d=a(85814),u=a.n(d),m=a(16189),p=a(43210),h=a(27605),x=a(45880),g=a(1510),b=a(54864),y=a(19150),f=a(68292),v=a(15566),j=a(32833),w=a(62163),N=a(40480),P=a(12810),_=a(77618),C=a(64668);let A=e=>x.z.object({name:x.z.string().min(1,e("fullNameRequired")),email:x.z.string().min(1,e("emailRequired")).email(e("invalidEmail")),password:x.z.string().min(1,e("passwordRequired")).min(8,e("passwordMin")).max(32,e("passwordMax")).regex(/[A-Z]/,e("passwordUppercase")).regex(/[a-z]/,e("passwordLowercase")).regex(/[0-9]/,e("passwordNumber")).regex(/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/,e("passwordSpecial")),confirmPassword:x.z.string().min(1,e("confirmPasswordRequired")),country:x.z.string().min(1,e("selectCountry")),sector:x.z.string().min(1,e("selectSector")),organizationType:x.z.string().min(1,e("selectOrgType"))}).refine(e=>e.password===e.confirmPassword,{message:e("passwordsDoNotMatch"),path:["confirmPassword"]}),S=()=>{let e=(0,_.c3)(),t=A(e),{register:a,formState:{errors:d,isSubmitting:x,isSubmitted:S},setValue:k,handleSubmit:E,setError:z,watch:M}=(0,h.mN)({resolver:(0,s.u)(t)}),R=M("password"),q=M("confirmPassword");(0,p.useEffect)(()=>{a("country",{required:e("selectCountry")}),a("sector",{required:e("selectSector")}),a("organizationType",{required:e("selectOrgType")})},[a]);let[L,T]=(0,p.useState)(""),[O,I]=(0,p.useState)(""),[H,U]=(0,p.useState)(""),[D,G]=(0,p.useState)(!1),[K,F]=(0,p.useState)(!1);(0,p.useEffect)(()=>{k("country",L,{shouldValidate:S}),k("sector",O,{shouldValidate:S}),k("organizationType",H,{shouldValidate:S})},[L,O,H,k]);let B=(0,m.useRouter)(),$=(0,b.wA)(),V=async t=>{try{await P.A.post("/users/signup",t),B.push("/"),$((0,y.Ds)({message:e("signupSuccess"),type:"success"}))}catch(t){t instanceof g.pe?z(t.response?.data.errorField,{message:t.response?.data.message}):$((0,y.Ds)({message:e("signupError"),type:"error"}))}};return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"flex flex-col gap-8 section w-11/12 mobile:w-4/5 tablet:w-2xl my-8 tablet:my-16",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,r.jsx)(i.A,{size:36}),(0,r.jsx)("h1",{className:"text-2xl tablet:text-3xl font-semibold text-center",children:e("createAccount")}),(0,r.jsx)("p",{className:"text-neutral-700 text-center",children:e("getStarted2")})]}),(0,r.jsxs)("form",{className:"flex flex-col gap-4",onSubmit:E(V),children:[(0,r.jsxs)("div",{className:"group label-input-group",children:[(0,r.jsx)("label",{htmlFor:"name",className:"label-text",children:e("fullName")}),(0,r.jsx)("input",{...a("name"),id:"name",type:"text",placeholder:e("enterFullName"),className:"input-field"}),d.name&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${d.name.message}`})]}),(0,r.jsxs)("div",{className:"group label-input-group",children:[(0,r.jsx)("label",{htmlFor:"email",className:"label-text",children:e("email")}),(0,r.jsx)("input",{...a("email"),id:"email",type:"email",placeholder:e("enterEmail"),className:"input-field"}),d.email&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${d.email.message}`})]}),(0,r.jsxs)("div",{className:"group label-input-group",children:[(0,r.jsx)("label",{htmlFor:"password",className:"label-text",children:e("password")}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{...a("password"),id:"password",type:D?"text":"password",placeholder:e("enterPassword"),className:"input-field w-full pr-10"}),R&&R.length>0&&(0,r.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>G(!D),children:[D?(0,r.jsx)(n.A,{className:"h-4 w-4"}):(0,r.jsx)(o.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{className:"sr-only",children:[D?"Hide":"Show"," password"]})]})]}),d.password&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${d.password.message}`})]}),(0,r.jsxs)("div",{className:"group label-input-group",children:[(0,r.jsx)("label",{htmlFor:"confirm-password",className:"label-text",children:e("confirmPassword")}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{...a("confirmPassword"),id:"confirm-password",type:K?"text":"password",placeholder:e("confirm_password_required"),className:"input-field w-full pr-10"}),q&&q.length>0&&(0,r.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>F(!K),children:[K?(0,r.jsx)(n.A,{className:"h-4 w-4"}):(0,r.jsx)(o.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{className:"sr-only",children:[K?"Hide":"Show"," password"]})]})]}),d.confirmPassword&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${d.confirmPassword.message}`})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 tablet:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"country",className:"label-text",children:[(0,r.jsx)(l.A,{size:16})," ",e("country")]}),(0,r.jsx)(f.l,{id:"country",options:v,value:L||e("selectOption"),onChange:T}),d.country&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${d.country.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"sector",className:"label-text",children:[(0,r.jsx)(c.A,{size:16})," ",e("sector")]}),(0,r.jsx)(f.l,{id:"sector",options:Object.values(j.b),value:O&&j.b[O]?j.b[O]:e("selectOption"),onChange:e=>{I((0,N.H)(e,j.b)??"")}}),d.sector&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${d.sector.message}`})]}),(0,r.jsxs)("div",{className:"label-input-group group",children:[(0,r.jsxs)("label",{htmlFor:"organizationType",className:"label-text",children:[(0,r.jsx)(c.A,{size:16})," ",e("organizationType")]}),(0,r.jsx)(f.l,{id:"organizationType",options:Object.values(w.i),value:H&&w.i[H]?w.i[H]:e("selectOption"),onChange:e=>{U((0,N.H)(e,w.i)??"")}}),d.organizationType&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${d.organizationType.message}`})]})]}),(0,r.jsx)("button",{type:"submit",className:"btn-primary",disabled:x,children:x?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[e("signingUp"),(0,r.jsx)("div",{className:"size-4 rounded-full border-x-2 animate-spin"})]}):e("signUp")})]}),(0,r.jsxs)("div",{className:"text-neutral-700 flex items-center gap-2",children:[(0,r.jsx)("span",{children:e("alreadyHaveAccount")}),(0,r.jsx)(u(),{href:"/",className:"font-medium hover:text-neutral-900 duration-300",children:e("signIn")})]}),(0,r.jsx)("div",{children:(0,r.jsx)(C.A,{})})]})})}},76565:(e,t,a)=>{var r={"./en.json":[87368,7368],"./ne.json":[3018,3018]};function s(e){if(!a.o(r,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=r[e],s=t[0];return a.e(t[1]).then(()=>a.t(s,19))}s.keys=()=>Object.keys(r),s.id=76565,e.exports=s},77832:(e,t,a)=>{"use strict";a.d(t,{ReduxProvider:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call ReduxProvider() from the server but ReduxProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReduxProvider.tsx","ReduxProvider")},78272:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79551:e=>{"use strict";e.exports=require("url")},81015:(e,t,a)=>{"use strict";a.d(t,{A:()=>n,V:()=>i});var r=a(35471);let s=["en","ne"];async function i(e){s.includes(e)||(console.warn(`Unsupported locale: ${e}, falling back to 'en'`),e="en");try{let t=(await a(76565)(`./${e}.json`)).default;if(!t||"object"!=typeof t)throw Error(`Invalid messages format for locale: ${e}`);return t}catch(t){if(console.error(`Failed to load messages for locale: ${e}`,t),"en"!==e)try{return console.log("Falling back to English messages"),(await a.e(7368).then(a.t.bind(a,87368,19))).default}catch(e){console.error("Failed to load fallback English messages",e)}return{}}}let n=(0,r.A)(async({locale:e})=>{let t=e?.toString()||"en";return{locale:t,messages:await i(t),timeZone:"Asia/Kathmandu",formats:{dateTime:{short:{day:"numeric",month:"short",year:"numeric"},medium:{day:"numeric",month:"long",year:"numeric"},long:{weekday:"long",day:"numeric",month:"long",year:"numeric"}},number:{currency:{style:"currency",currency:"NPR"}}}}})},81630:e=>{"use strict";e.exports=require("http")},82704:()=>{},83997:e=>{"use strict";e.exports=require("tty")},86778:(e,t,a)=>{Promise.resolve().then(a.bind(a,44395)),Promise.resolve().then(a.bind(a,60265)),Promise.resolve().then(a.bind(a,77832))},89011:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>n,dQ:()=>s,g7:()=>i});let r=(0,a(9317).Z0)({initialState:{visible:!1,option:""},name:"createLibraryItem",reducers:{showCreateLibraryItemModal:(e,t)=>{e.visible=!0,e.option=t.payload},hideCreateLibraryItemModal:e=>{e.visible=!1,e.option=""}}}),{showCreateLibraryItemModal:s,hideCreateLibraryItemModal:i}=r.actions,n=r.reducer},93091:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\[locale]\\\\(auth)\\\\signup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\[locale]\\(auth)\\signup\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,7404,1658,6560,8610,5374,2198,5814,5823],()=>a(54306));module.exports=r})();