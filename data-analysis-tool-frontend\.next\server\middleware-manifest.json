{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|images|fonts|api).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|images|fonts|api).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "np3ReC8xrXujWBt-bv4UK", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vxTVhA8XalJO92PbSKJ7lk2zRCVwDY/NQi5gp+1+9f4=", "__NEXT_PREVIEW_MODE_ID": "a481896acbd21ca7698c5a98475a15b4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "14be5ab1729ee0f7441248a5cbf39ebf1627402db80555fa189208c0fd1fd343", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "04bb86dee29705cd7c09c022073f721100f24279f511abee724f5f965d2c2f3d"}}}, "functions": {}, "sortedMiddleware": ["/"]}