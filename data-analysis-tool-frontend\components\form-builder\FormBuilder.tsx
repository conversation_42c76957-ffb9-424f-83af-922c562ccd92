"use client";

import React, { useState } from "react";
import {
  Dnd<PERSON>ontext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
  arrayMove,
} from "@dnd-kit/sortable";
import { Question, QuestionGroup } from "@/types/formBuilder";
import { QuestionItem } from "@/components/form-builder/QuestionItem";
import { QuestionGroupItem } from "@/components/form-builder/QuestionGroupItem";
import {
  Eye,
  FileDown,
  FileUp,
  PlusCircle,
  Settings,
  BookOpen,
  FolderPlus,
} from "lucide-react";
import { AddQuestionModal } from "../modals/AddQuestionModal";
import { EditQuestionModal } from "../modals/EditQuestionModal";
import { EditTableQuestionModal } from "../modals/EditTableQuestionModal";
import { ConfirmationModal } from "../modals/ConfirmationModal";
import { QuestionGroupModal } from "../modals/QuestionGroupModal";
import { DeleteQuestionGroupModal } from "../modals/DeleteQuestionGroupModal";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  deleteQuestion,
  duplicateQuestion,
  addQuestion,
  updateQuestionPositions,
} from "@/lib/api/form-builder";
import {
  fetchQuestionGroups,
  createQuestionGroup,
  updateQuestionGroup,
  deleteQuestionGroup,
  deleteQuestionAndGroup,
  moveQuestionBetweenGroups,
  moveGroupInsideGroup,
  removeGroupFromParent,
  updateGroupPositions,
} from "@/lib/api/question-groups";
import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";
import { ContextType } from "@/types";
import { LoadingOverlay } from "../general/LoadingOverlay";
import LibraryQuestionsSidebar from "./LibraryQuestionsSidebar";
import { ProjectPermissionFlags } from "@/types";
import { useTranslations } from "next-intl";

const FormBuilder = ({
  setIsPreviewMode,
  questions,
  contextType,
  contextId,
  permissions,
}: {
  setIsPreviewMode: React.Dispatch<React.SetStateAction<boolean>>;
  questions: Question[];
  contextType: ContextType;
  contextId: number;
  permissions: ProjectPermissionFlags;
}) => {
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor)
  );

  const t = useTranslations();

  const editFormPermission = permissions.manageProject || permissions.editForm;

  // Question modals state
  const [showAddQuestionModal, setShowAddQuestionModal] =
    useState<boolean>(false);
  const [showEditQuestionModal, setShowEditQuestionModal] =
    useState<boolean>(false);
  const [showEditTableQuestionModal, setShowEditTableQuestionModal] =
    useState<boolean>(false);
  const [showLibrarySidebar, setShowLibrarySidebar] = useState<boolean>(false);
  const [isAddingQuestions, setIsAddingQuestions] = useState<boolean>(false);
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null);
  const [showDeleteConfirmationModal, setShowDeleteConfirmationModal] =
    useState<boolean>(false);

  // Question group modals state
  const [showAddGroupModal, setShowAddGroupModal] = useState<boolean>(false);
  const [showEditGroupModal, setShowEditGroupModal] = useState<boolean>(false);
  const [showDeleteGroupModal, setShowDeleteGroupModal] =
    useState<boolean>(false);
  const [currentGroupId, setCurrentGroupId] = useState<number | null>(null);

  // Question selection state
  const [selectedQuestionIds, setSelectedQuestionIds] = useState<number[]>([]);
  const [isEditingGroupName, setIsEditingGroupName] = useState<number | null>(
    null
  );
  const [editingGroupName, setEditingGroupName] = useState<string>("");
  const [selectionMode, setSelectionMode] = useState<boolean>(false);

  // Subgroup creation state
  const [showSubgroupNameModal, setShowSubgroupNameModal] = useState(false);
  const [subgroupParentId, setSubgroupParentId] = useState<number | null>(null);
  const [subgroupSelectedQuestions, setSubgroupSelectedQuestions] = useState<number[]>([]);
  const [subgroupName, setSubgroupName] = useState("");

  // State for tracking when questions are being added
  const [isProcessingGroup, setIsProcessingGroup] = useState(false);

  const dispatch = useDispatch();
  const queryClient = useQueryClient();

  const questionsQueryKey =
    contextType === "project"
      ? ["questions", contextId]
      : contextType === "template"
      ? ["templateQuestions", contextId]
      : ["questionBlockQuestions", contextId];

  const groupsQueryKey = ["questionGroups", contextId];

  // Fetch question groups
  const { data: questionGroups = [], isLoading: isLoadingGroups } = useQuery({
    queryKey: groupsQueryKey,
    queryFn: () => fetchQuestionGroups({ projectId: contextId }),
    enabled: contextType === "project", // Only fetch for projects, not templates or question blocks
  });

  // Group questions by their group ID and sort by position within each group
  const groupedQuestions = questionGroups.reduce(
    (acc: Record<number, Question[]>, group: QuestionGroup) => {
      acc[group.id] = questions
        .filter((q) => q.questionGroupId === group.id)
        .sort((a, b) => a.position - b.position);
      return acc;
    },
    {} as Record<number, Question[]>
  );

  // Get ungrouped questions
  const ungroupedQuestions = questions.filter((q) => !q.questionGroupId);

  // Build nested group structure
  const buildNestedGroups = (groups: QuestionGroup[]): QuestionGroup[] => {
    const groupMap = new Map<number, QuestionGroup>();

    // Create a map of all groups with their subGroups and questions initialized
    groups.forEach(group => {
      // Get questions for this group
      const groupQuestions = questions
        .filter((q) => q.questionGroupId === group.id)
        .sort((a, b) => a.position - b.position);

      groupMap.set(group.id, {
        ...group,
        subGroups: [],
        question: groupQuestions
      });
    });

    // Build the nested structure
    const topLevelGroups: QuestionGroup[] = [];
    groups.forEach(group => {
      const groupWithSubGroups = groupMap.get(group.id)!;

      if (group.parentGroupId) {
        // This is a child group, add it to its parent's subGroups
        const parentGroup = groupMap.get(group.parentGroupId);
        if (parentGroup) {
          parentGroup.subGroups = parentGroup.subGroups || [];
          parentGroup.subGroups.push(groupWithSubGroups);
        }
      } else {
        // This is a top-level group
        topLevelGroups.push(groupWithSubGroups);
      }
    });

    return topLevelGroups;
  };

  // Rebuild nested groups whenever questions or questionGroups change
  const nestedQuestionGroups = React.useMemo(() => {
    return buildNestedGroups(questionGroups);
  }, [questionGroups, questions]);

  // Create a unified list of form items (groups and individual questions) for dynamic ordering
  const createUnifiedFormItems = () => {
    const items: Array<{
      type: 'group' | 'question';
      data: QuestionGroup | Question;
      order: number;
      originalPosition?: number; // Track original position for better sorting
    }> = [];

    // Only add question groups for projects (not templates or question blocks)
    // Only show top-level groups (groups without a parent) in the main list
    if (contextType === "project") {
      nestedQuestionGroups.forEach((group: QuestionGroup) => {
        // For groups, find the minimum position of questions in the group
        // This ensures the group appears where the first question was originally positioned
        const groupQuestions = questions
          .filter(q => q.questionGroupId === group.id)
          .sort((a, b) => a.position - b.position);
        const minQuestionPosition = groupQuestions.length > 0
          ? Math.min(...groupQuestions.map(q => q.position))
          : group.order;

        items.push({
          type: 'group',
          data: group,
          order: minQuestionPosition, // Use the position of the first question in the group
          originalPosition: minQuestionPosition
        });
      });
    }

    // Add ungrouped questions (or all questions if not in project context)
    const questionsToAdd = contextType === "project" ? ungroupedQuestions : questions;
    questionsToAdd.forEach((question: Question) => {
      items.push({
        type: 'question',
        data: question,
        order: question.position,
        originalPosition: question.position
      });
    });

    // Sort by order/position, with a secondary sort by type to ensure consistent ordering
    // when groups and questions have the same position
    return items.sort((a, b) => {
      if (a.order === b.order) {
        // If positions are equal, prioritize based on original question positions
        // This helps maintain the original flow when grouping questions
        return (a.originalPosition || a.order) - (b.originalPosition || b.order);
      }
      return a.order - b.order;
    });
  };

  // Memoize unified form items to prevent unnecessary recalculations
  const unifiedFormItems = React.useMemo(() => {
    return createUnifiedFormItems();
  }, [nestedQuestionGroups, ungroupedQuestions, contextType]);

  // Force refresh of question groups if they're empty but we have questions with questionGroupId
  React.useEffect(() => {
    const hasGroupedQuestions = questions.some(
      (q) => q.questionGroupId !== null && q.questionGroupId !== undefined
    );
    const hasNoGroups = questionGroups.length === 0;

    if (hasGroupedQuestions && hasNoGroups && contextType === "project") {
      queryClient.invalidateQueries({ queryKey: groupsQueryKey });
    }
  }, [questions, questionGroups, contextType, queryClient, groupsQueryKey]);

  // Question mutations
  const deleteQuestionMutation = useMutation({
    mutationFn: deleteQuestion,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: questionsQueryKey });
      dispatch(
        showNotification({
          message: t('questionDeleted'),
          type: "success",
        })
      );
    },
    onError: () => {
      dispatch(
        showNotification({
          message: t('questionDeleteFailed'),
          type: "error",
        })
      );
    },
    onSettled: () => {
      setShowDeleteConfirmationModal(false);
    },
  });

  const duplicateQuestionMutation = useMutation({
    mutationFn: duplicateQuestion,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: questionsQueryKey });
      dispatch(
        showNotification({
          message: t('questionDuplicated'),
          type: "success",
        })
      );
    },
    onError: () => {
      dispatch(
        showNotification({
          message: t('questionDuplicateFailed'),
          type: "error",
        })
      );
    },
    onSettled: () => {
      setShowDeleteConfirmationModal(false);
    },
  });

  // Question group mutations
  const deleteGroupMutation = useMutation({
    mutationFn: deleteQuestionGroup,
    onSuccess: (data, variables) => {

      // Find the group that was deleted
      const deletedGroup = questionGroups.find(
        (g: QuestionGroup) => g.id === variables.id
      );

      if (deletedGroup) {
        // Find questions that belonged to this group
        const questionsInGroup = questions.filter(
          (q) => q.questionGroupId === deletedGroup.id
        );

        if (questionsInGroup.length > 0) {
          // Update the local state to mark these questions as ungrouped
          const updatedQuestions = questions.map((q) =>
            q.questionGroupId === deletedGroup.id
              ? { ...q, questionGroupId: undefined }
              : q
          );

          // Update the questions in the local state
          queryClient.setQueryData(questionsQueryKey, updatedQuestions);

          // Remove the deleted group from the local state
          const updatedGroups = questionGroups.filter(
            (g: QuestionGroup) => g.id !== variables.id
          );
          queryClient.setQueryData(groupsQueryKey, updatedGroups);
        }
      }

      // Then invalidate queries to get the latest data from the server
      queryClient.invalidateQueries({ queryKey: groupsQueryKey });
      queryClient.invalidateQueries({ queryKey: questionsQueryKey });

      dispatch(
        showNotification({
          message: t('groupDeleted'),
          type: "success",
        })
      );

      setShowDeleteGroupModal(false);
      setIsProcessingGroup(false);
    },
    onError: (error) => {
      console.error(t('groupDeleteError'), error);
      dispatch(
        showNotification({
          message: t('groupDeleteFailed'),
          type: "error",
        })
      );
      setIsProcessingGroup(false);
    },
  });

  const deleteGroupWithQuestionsMutation = useMutation({
    mutationFn: deleteQuestionAndGroup,
    onSuccess: (data, variables) => {

      // Find the group that was deleted
      const deletedGroup = questionGroups.find(
        (g: QuestionGroup) => g.id === variables.id
      );

      if (deletedGroup) {
        // Remove the deleted group from the local state
        const updatedGroups = questionGroups.filter(
          (g: QuestionGroup) => g.id !== variables.id
        );
        queryClient.setQueryData(groupsQueryKey, updatedGroups);

        // Remove questions that belonged to this group from the local state
        const updatedQuestions = questions.filter(
          (q) => q.questionGroupId !== deletedGroup.id
        );
        queryClient.setQueryData(questionsQueryKey, updatedQuestions);
      }

      // Then invalidate queries to get the latest data from the server
      queryClient.invalidateQueries({ queryKey: groupsQueryKey });
      queryClient.invalidateQueries({ queryKey: questionsQueryKey });

      dispatch(
        showNotification({
          message: t('groupAndQuestionsDeleted'),
          type: "success",
        })
      );

      setShowDeleteGroupModal(false);
      setIsProcessingGroup(false);
    },
    onError: (error) => {
      console.error(t('groupAndQuestionsDeleteError'), error);
      dispatch(
        showNotification({
          message:
            t('groupAndQuestionsDeleteFailed'),
          type: "error",
        })
      );
      setIsProcessingGroup(false);
    },
  });

  // Note: We'll need a mutation for removing questions from groups in the future
  // This functionality will be implemented when needed

  // Question position update mutation
  const updatePositionsMutation = useMutation({
    mutationFn: updateQuestionPositions,
    onMutate: async (variables) => {
      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: questionsQueryKey });

      // Snapshot the previous value
      const previousQuestions = queryClient.getQueryData(questionsQueryKey);

      // Optimistically update the local state
      if (previousQuestions && variables.questionPositions) {
        const updatedQuestions = (previousQuestions as Question[]).map((question) => {
          const newPosition = variables.questionPositions.find(
            (pos) => pos.id === question.id
          );
          return newPosition ? { ...question, position: newPosition.position } : question;
        });

        queryClient.setQueryData(questionsQueryKey, updatedQuestions);
      }

      // Return a context object with the snapshotted value
      return { previousQuestions };
    },
    onSuccess: () => {
      // Invalidate and refetch to ensure we have the latest data from server
      queryClient.invalidateQueries({ queryKey: questionsQueryKey });
      dispatch(
        showNotification({
          message: t('questionOrderUpdated'),
          type: "success",
        })
      );
    },
    onError: (error: any, variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousQuestions) {
        queryClient.setQueryData(questionsQueryKey, context.previousQuestions);
      }

      console.error("Failed to update question positions:", error);
      console.error("Error response:", error.response?.data);
      dispatch(
        showNotification({
          message: `${t('questionOrderUpdateFailed')}: ${error.response?.data?.message || error.message || t('tryAgain')}`,
          type: "error",
        })
      );
    },
    onSettled: () => {
      // Always refetch after error or success to ensure we have the correct data
      queryClient.invalidateQueries({ queryKey: questionsQueryKey });
    },
  });

  // Move question between groups mutation
  const moveQuestionBetweenGroupsMutation = useMutation({
    mutationFn: moveQuestionBetweenGroups,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: questionsQueryKey });
      queryClient.invalidateQueries({ queryKey: groupsQueryKey });
      dispatch(
        showNotification({
          message: "Question moved successfully",
          type: "success",
        })
      );
    },
    onError: (error: any) => {
      console.error("Failed to move question between groups:", error);
      dispatch(
        showNotification({
          message: `Failed to move question: ${
            error.response?.data?.message || error.message || "Please try again"
          }`,
          type: "error",
        })
      );
    },
  });

  // Move group inside another group mutation
  const moveGroupInsideGroupMutation = useMutation({
    mutationFn: moveGroupInsideGroup,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: groupsQueryKey });
      dispatch(
        showNotification({
          message: "Group moved successfully",
          type: "success",
        })
      );
    },
    onError: (error: any) => {
      console.error("Failed to move group:", error);
      dispatch(
        showNotification({
          message: `Failed to move group: ${
            error.response?.data?.message || error.message || "Please try again"
          }`,
          type: "error",
        })
      );
    },
  });

  // Update group positions mutation
  const updateGroupPositionsMutation = useMutation({
    mutationFn: updateGroupPositions,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: groupsQueryKey });
      dispatch(
        showNotification({
          message: "Group order updated successfully",
          type: "success",
        })
      );
    },
    onError: (error: any) => {
      console.error("Failed to update group positions:", error);
      dispatch(
        showNotification({
          message: `Failed to update group order: ${
            error.response?.data?.message || error.message || "Please try again"
          }`,
          type: "error",
        })
      );
    },
    onSettled: () => {
      // Always refetch after error or success to ensure we have the correct data
      queryClient.invalidateQueries({ queryKey: questionsQueryKey });
    },
  });

  // Move question between groups mutation
  const moveQuestionBetweenGroupsMutation = useMutation({
    mutationFn: moveQuestionBetweenGroups,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: questionsQueryKey });
      queryClient.invalidateQueries({ queryKey: groupsQueryKey });
      dispatch(
        showNotification({
          message: "Question moved successfully",
          type: "success",
        })
      );
    },
    onError: (error: any) => {
      console.error("Failed to move question between groups:", error);
      dispatch(
        showNotification({
          message: `Failed to move question: ${
            error.response?.data?.message || error.message || "Please try again"
          }`,
          type: "error",
        })
      );
    },
  });

  // Move group inside another group mutation
  const moveGroupInsideGroupMutation = useMutation({
    mutationFn: moveGroupInsideGroup,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: groupsQueryKey });
      dispatch(
        showNotification({
          message: "Group moved successfully",
          type: "success",
        })
      );
    },
    onError: (error: any) => {
      console.error("Failed to move group:", error);
      dispatch(
        showNotification({
          message: `Failed to move group: ${
            error.response?.data?.message || error.message || "Please try again"
          }`,
          type: "error",
        })
      );
    },
  });

  // Update group positions mutation
  const updateGroupPositionsMutation = useMutation({
    mutationFn: updateGroupPositions,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: groupsQueryKey });
      dispatch(
        showNotification({
          message: "Group order updated successfully",
          type: "success",
        })
      );
    },
    onError: (error: any) => {
      console.error("Failed to update group positions:", error);
      dispatch(
        showNotification({
          message: `Failed to update group order: ${
            error.response?.data?.message || error.message || "Please try again"
          }`,
          type: "error",
        })
      );
    },
  });

  // Question handlers
  const handleDelete = () => {
    if (currentQuestion && currentQuestion.id) {
      deleteQuestionMutation.mutate({
        contextType,
        id: currentQuestion?.id,
        projectId: contextId,
      });
    }
  };

  // Enhanced drag-and-drop handlers
  const handleMoveQuestionBetweenGroups = (
    questionId: number,
    fromGroupId: number | null,
    toGroupId: number | null
  ) => {
    if (fromGroupId === toGroupId) return;

    moveQuestionBetweenGroupsMutation.mutate({
      questionId,
      groupId: fromGroupId || 0, // Use 0 for ungrouped questions
      newGroupId: toGroupId || 0, // Use 0 for ungrouped questions
    });
  };

  const handleMoveGroupInsideGroup = (
    childGroupId: number,
    parentGroupId: number | null
  ) => {
    if (parentGroupId) {
      moveGroupInsideGroupMutation.mutate({
        childGroupId,
        parentGroupId,
      });
    }
  };

  // Subgroup creation handlers
  const handleCreateSubgroup = (parentGroupId: number, selectedQuestionIds: number[]) => {
    setSubgroupParentId(parentGroupId);
    setSubgroupSelectedQuestions(selectedQuestionIds);
    setShowSubgroupNameModal(true);
  };

  const handleSubgroupNameSubmit = () => {
    if (!subgroupName.trim() || !subgroupParentId || subgroupSelectedQuestions.length === 0) {
      return;
    }

    // Create the subgroup
    // Calculate a reasonable order value
    const maxOrder = questionGroups.length > 0
      ? Math.max(...questionGroups.map((g: QuestionGroup) => g.order))
      : 0;

    createGroupMutation.mutate({
      title: subgroupName.trim(),
      order: maxOrder + 1, // Use next available order
      projectId: contextId,
      selectedQuestionIds: subgroupSelectedQuestions,
      parentGroupId: subgroupParentId,
    });

    // Reset state
    setShowSubgroupNameModal(false);
    setSubgroupName("");
    setSubgroupParentId(null);
    setSubgroupSelectedQuestions([]);

    // Clear selection
    setSelectedQuestionIds(prev =>
      prev.filter(id => !subgroupSelectedQuestions.includes(id))
    );
  };

  const handleCancelSubgroup = () => {
    setShowSubgroupNameModal(false);
    setSubgroupName("");
    setSubgroupParentId(null);
    setSubgroupSelectedQuestions([]);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id) {
      return;
    }

    // Only handle drag and drop in project context
    if (contextType !== "project") {
      return;
    }

    const activeData = active.data.current;
    const overData = over.data.current;

    // Handle question reordering within the same group
    if (activeData?.type === 'question' && overData?.type === 'question') {
      const activeQuestion = questions.find((q) => q.id === active.id);
      const overQuestion = questions.find((q) => q.id === over.id);

      if (!activeQuestion || !overQuestion) {
        return;
      }

      // Handle reordering within the same group or ungrouped questions
      const isSameGroup =
        activeQuestion.questionGroupId === overQuestion.questionGroupId;

      if (!isSameGroup) {
        return;
      }

      // Get questions in the same context (same group or ungrouped)
      const contextQuestions = questions
        .filter((q) => q.questionGroupId === activeQuestion.questionGroupId)
        .sort((a, b) => a.position - b.position);

      const oldIndex = contextQuestions.findIndex((q) => q.id === active.id);
      const newIndex = contextQuestions.findIndex((q) => q.id === over.id);

      if (oldIndex === -1 || newIndex === -1) {
        return;
      }

      // Reorder the questions array
      const reorderedQuestions = arrayMove(contextQuestions, oldIndex, newIndex);

      // Calculate new positions for all affected questions
      const questionPositions = reorderedQuestions.map((question, index) => ({
        id: Number(question.id), // Ensure it's a number
        position: index + 1, // Start positions from 1
      }));

      // Optimistically update the local state immediately
      const updatedQuestions = questions.map((question) => {
        const newPosition = questionPositions.find((pos) => pos.id === question.id);
        return newPosition ? { ...question, position: newPosition.position } : question;
      });
      queryClient.setQueryData(questionsQueryKey, updatedQuestions);

      // Update positions in the backend
      updatePositionsMutation.mutate({
        contextType,
        contextId,
        questionPositions,
      });
    }

    // Handle moving questions between groups
    if (activeData?.type === 'question' && overData?.type === 'group-drop') {
      const questionId = Number(active.id);
      const fromGroupId = activeData.questionGroupId || null;
      const toGroupId = overData.groupId;

      handleMoveQuestionBetweenGroups(questionId, fromGroupId, toGroupId);
    }

    // Handle moving groups inside other groups
    if (activeData?.type === 'group' && overData?.type === 'group-drop') {
      const childGroupId = activeData.groupId;
      const parentGroupId = overData.groupId;

      // Prevent dropping a group into itself or its descendants
      if (childGroupId !== parentGroupId) {
        handleMoveGroupInsideGroup(childGroupId, parentGroupId);
      }
    }

    // Handle group reordering
    if (activeData?.type === 'group' && overData?.type === 'group') {
      // This would handle reordering groups at the same level
      // Implementation depends on specific requirements
    }
  };

  // Group handlers
  const handleEditGroup = (groupId: number) => {
    setCurrentGroupId(groupId);
    setShowEditGroupModal(true);
  };

  const handleDeleteGroup = (groupId: number) => {
    setCurrentGroupId(groupId);
    setShowDeleteGroupModal(true);
  };

  const handleConfirmDeleteGroup = () => {
    if (currentGroupId) {
      setIsProcessingGroup(true);
      deleteGroupMutation.mutate({ id: currentGroupId });
    }
  };

  const handleConfirmDeleteGroupWithQuestions = () => {
    if (currentGroupId) {
      setIsProcessingGroup(true);
      deleteGroupWithQuestionsMutation.mutate({ id: currentGroupId });
    }
  };

  const handleAddQuestionToGroup = (groupId: number) => {
    setCurrentGroupId(groupId);
    setShowEditGroupModal(true);
  };

  // Handle toggling question selection
  const toggleQuestionSelection = (questionId: number) => {
    setSelectedQuestionIds((prev) =>
      prev.includes(questionId)
        ? prev.filter((id) => id !== questionId)
        : [...prev, questionId]
    );
  };

  // Create group mutation
  const createGroupMutation = useMutation({
    mutationFn: createQuestionGroup,
    onSuccess: (data, variables) => {

      // Update local state immediately for a smoother UI experience
      // This will show the grouped questions before the server refetch completes
      const newGroupId = data.data?.questionGroup?.id;

      if (newGroupId && variables.selectedQuestionIds) {

        // Update the questionGroupId for selected questions in the local state
        const updatedQuestions = questions.map((q) =>
          variables.selectedQuestionIds?.includes(q.id)
            ? { ...q, questionGroupId: newGroupId }
            : q
        );

        // Update the local state with the new questions
        queryClient.setQueryData(questionsQueryKey, updatedQuestions);

        // Also update the groups in the local state
        const newGroup = {
          id: newGroupId,
          title: variables.title,
          order: variables.order,
          projectId: variables.projectId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          question: updatedQuestions.filter(
            (q) => q.questionGroupId === newGroupId
          ),
        };

        queryClient.setQueryData(groupsQueryKey, [...questionGroups, newGroup]);
      }

      // Then invalidate queries to get the latest data from the server
      queryClient.invalidateQueries({ queryKey: groupsQueryKey });
      queryClient.invalidateQueries({ queryKey: questionsQueryKey });

      dispatch(
        showNotification({
          message: t('groupCreated'),
          type: "success",
        })
      );

      // Clear selection after creating group
      setSelectedQuestionIds([]);
      setSelectionMode(false);
      setIsProcessingGroup(false);
    },
    onError: (error) => {
      console.error(t('groupCreateError'), error);
      dispatch(
        showNotification({
          message: t('groupCreateFailed'),
          type: "error",
        })
      );
      setIsProcessingGroup(false);
    },
  });

  // Handle creating a group from selected questions
  const handleCreateGroupFromSelected = () => {
    if (selectedQuestionIds.length === 0) {
      dispatch(
        showNotification({
          message: t('selectAtLeastOneQuestion'),
          type: "warning",
        })
      );
      return;
    }

    setIsProcessingGroup(true);

    // Calculate the group order based on the minimum position of selected questions
    // This ensures the group appears in the same position as the first selected question
    const selectedQuestions = questions.filter(q => selectedQuestionIds.includes(q.id));
    const minPosition = selectedQuestions.length > 0
      ? Math.min(...selectedQuestions.map(q => q.position))
      : questionGroups.length + 1;

    // Create a new group with the selected questions
    createGroupMutation.mutate({
      title: t('newGroup'),
      order: minPosition,
      projectId: contextId,
      selectedQuestionIds,
    });
  };

  // Handle inline editing of group name
  const startEditingGroupName = (groupId: number, currentName: string) => {
    setIsEditingGroupName(groupId);
    setEditingGroupName(currentName);
  };

  // Handle canceling the editing of group name
  const cancelEditingGroupName = () => {
    setIsEditingGroupName(null);
    setEditingGroupName("");
  };

  // Update group mutation
  const updateGroupMutation = useMutation({
    mutationFn: updateQuestionGroup,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: groupsQueryKey });
      dispatch(
        showNotification({
          message: t('groupNameUpdated'),
          type: "success",
        })
      );
      setIsEditingGroupName(null);
      setEditingGroupName("");
      setIsProcessingGroup(false);
    },
    onError: () => {
      dispatch(
        showNotification({
          message: t('groupNameUpdateFailed'),
          type: "error",
        })
      );
      setIsProcessingGroup(false);
    },
  });

  const saveGroupName = (groupId: number) => {
    if (!editingGroupName.trim()) {
      dispatch(
        showNotification({
          message: t('groupNameEmpty'),
          type: "warning",
        })
      );
      return;
    }

    setIsProcessingGroup(true);

    const group = questionGroups.find((g: QuestionGroup) => g.id === groupId);
    if (!group) return;

    // Update the group name in the local state immediately for better UX
    const updatedGroups = questionGroups.map((g: QuestionGroup) =>
      g.id === groupId ? { ...g, title: editingGroupName } : g
    );
    queryClient.setQueryData(groupsQueryKey, updatedGroups);

    // Then send the update to the server
    updateGroupMutation.mutate({
      id: groupId,
      title: editingGroupName,
      order: group.order,
    });
  };

  // Mutation for adding a single question
  const addQuestionMutation = useMutation({
    mutationFn: addQuestion,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: questionsQueryKey });
    },
    onError: (error) => {
      console.error(t('addQuestionError'), error);
      dispatch(
        showNotification({
          message: t('addQuestionFailed'),
          type: "error",
        })
      );
    },
  });

  // Function to handle adding questions from the library
  const handleAddQuestionsFromLibrary = async (
    selectedQuestions: Question[]
  ) => {
    if (selectedQuestions.length === 0) return;

    setIsAddingQuestions(true);

    try {
      // Calculate the starting position for new questions
      const maxPosition = questions.length > 0 ? Math.max(...questions.map(q => q.position)) : 0;

      // Process each selected question sequentially
      for (let i = 0; i < selectedQuestions.length; i++) {
        const question = selectedQuestions[i];
        const dataToSend = {
          label: question.label,
          isRequired: question.isRequired,
          hint: question.hint || "",
          placeholder: question.placeholder || "",
          inputType: String(question.inputType), // Convert to string to ensure compatibility
          questionOptions: question.questionOptions || [],
        };

        await addQuestion({
          contextType,
          contextId,
          dataToSend,
          position: maxPosition + i + 1, // Add questions at the end
        });
      }

      // Refresh the questions list
      queryClient.invalidateQueries({ queryKey: questionsQueryKey });

      dispatch(
        showNotification({
          message: `${selectedQuestions.length} ${t('questionsAdded')}`,
          type: "success",
        })
      );
    } catch (error) {
      console.error("Error adding questions:", error);
      dispatch(
        showNotification({
          message: t('addFromLibraryFailed'),
          type: "error",
        })
      );
    } finally {
      setIsAddingQuestions(false);
    }
  };
  return (
    <div className="min-h-[60vh] relative">
      {(deleteQuestionMutation.isPending ||
        duplicateQuestionMutation.isPending ||
        deleteGroupMutation.isPending ||
        deleteGroupWithQuestionsMutation.isPending ||
        updatePositionsMutation.isPending ||
        isAddingQuestions) && <LoadingOverlay />}

      {/* For adding new questions */}
      <AddQuestionModal
        showModal={showAddQuestionModal}
        setShowModal={setShowAddQuestionModal}
        contextType={contextType}
        contextId={contextId}
        position={questions.length > 0 ? Math.max(...questions.map(q => q.position)) + 1 : 1}
      />

      {/* for editing existing questions, it requires a question object as props */}
      {currentQuestion && currentQuestion.inputType !== "table" && (
        <EditQuestionModal
          showModal={showEditQuestionModal}
          setShowModal={setShowEditQuestionModal}
          contextType={contextType}
          question={currentQuestion}
          contextId={contextId}
        />
      )}

      {/* for editing table questions specifically */}
      {currentQuestion && currentQuestion.inputType === "table" && (
        <EditTableQuestionModal
          showModal={showEditTableQuestionModal}
          setShowModal={setShowEditTableQuestionModal}
          contextType={contextType}
          question={currentQuestion}
          contextId={contextId}
        />
      )}

      {/* For adding/editing question groups */}
      <QuestionGroupModal
        showModal={showAddGroupModal}
        setShowModal={setShowAddGroupModal}
        contextType={contextType}
        contextId={contextId}
        questions={questions}
        questionGroups={questionGroups}
      />

      {/* For editing existing question groups */}
      {currentGroupId && (
        <QuestionGroupModal
          showModal={showEditGroupModal}
          setShowModal={setShowEditGroupModal}
          contextType={contextType}
          contextId={contextId}
          existingGroup={questionGroups.find(
            (g: QuestionGroup) => g.id === currentGroupId
          )}
          questions={questions}
          questionGroups={questionGroups}
        />
      )}

      {/* Delete question confirmation modal */}
      <ConfirmationModal
        showModal={showDeleteConfirmationModal}
        onClose={() => setShowDeleteConfirmationModal(false)}
        onConfirm={handleDelete}
        title= {t('deleteQuestion')}
        description= {t('confirmDeleteQuestion')}
        confirmButtonText= {t('delete')}
        cancelButtonText= {t('cancel')}
        confirmButtonClass="btn-danger"
      />

      {/* Delete group confirmation modal */}
      <DeleteQuestionGroupModal
        showModal={showDeleteGroupModal}
        setShowModal={setShowDeleteGroupModal}
        onConfirmDelete={handleConfirmDeleteGroup}
        onConfirmDeleteWithQuestions={handleConfirmDeleteGroupWithQuestions}
        isDeleting={
          deleteGroupMutation.isPending ||
          deleteGroupWithQuestionsMutation.isPending
        }
      />

      {/* Library questions sidebar */}
      <LibraryQuestionsSidebar
        isOpen={showLibrarySidebar}
        onClose={() => setShowLibrarySidebar(false)}
        onAddQuestions={handleAddQuestionsFromLibrary}
      />

      {/* Header with actions */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center">
          <h1 className="heading-text mr-4"> {t('formBuilder')} </h1>

          {/* Create Group button */}
          {selectedQuestionIds.length > 0 ? (
            <button
              className="btn-primary flex items-center gap-2"
              onClick={handleCreateGroupFromSelected}
              disabled={isProcessingGroup}
            >
              <FolderPlus size={16} />
              {t('createGroup')} ({selectedQuestionIds.length})
            </button>
          ) : (
            <div className="flex gap-2">
              <button
                className="btn-outline flex items-center gap-2"
                onClick={() => {
                  // Toggle selection mode
                  if (!selectionMode) {
                    setSelectionMode(true);
                  } else {
                    setSelectionMode(false);
                    setSelectedQuestionIds([]);
                  }
                }}
              >
                <FolderPlus size={16} />
                {selectionMode ? t('cancelSelection') : t('selectQuestions')}
              </button>

              <button
                className="btn-outline flex items-center gap-2"
                onClick={() => setShowAddGroupModal(true)}
              >
                <FolderPlus size={16} />
                {t('createEmptyGroup')}
              </button>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <button
            className="btn-outline p-2"
            onClick={() => setIsPreviewMode(true)}
            title={t('previewForm')}
          >
            <Eye size={16} />
          </button>
          <button
            className="btn-outline p-2"
            onClick={() => setShowLibrarySidebar(true)}
            title={t('questionLibrary')}
          >
            <BookOpen size={16} />
          </button>
          {/* <button
            className="btn-outline p-2"
            onClick={() => {}}
            title="Export Form"
          >
            <FileDown size={16} />
          </button>
          <button
            className="btn-outline p-2"
            onClick={() => {}}
            title="Import Form"
          >
            <FileUp size={16} />
          </button>
          <button className="btn-outline p-2" title="Settings">
            <Settings size={16} />
          </button> */}
        </div>
      </div>

      {/* Main content area */}
      <div className="section shadow-none border border-neutral-400">
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={[
              ...questions.map((question) => question.id),
              ...questionGroups.map((group: QuestionGroup) => `group-${group.id}`)
            ]}
            strategy={verticalListSortingStrategy}
          >
            <div className="space-y-4">
              {questions.length === 0 ? (
                // No questions at all
                <div className="text-center py-16 px-4">
                  <h3 className="heading-text text-muted-foreground">
                    {t('noQuestionsYet')}
                  </h3>
                  <p className="mt-1 text-sm sub-text">
                    {t('addFirstQuestion')}
                  </p>
                  <div className="p-4 flex justify-center">
                    <button
                      onClick={() => setShowAddQuestionModal(true)}
                      className="btn-primary"
                      disabled={!editFormPermission}
                    >
                      <PlusCircle size={16} />
                      {t('addFirst')}
                    </button>
                  </div>
                </div>
              ) : (
                // Render unified form items (groups and individual questions) in order
                unifiedFormItems.map((item) => {
                  if (item.type === 'group') {
                    const group = item.data as QuestionGroup;
                    // Use questions from the nested group structure instead of filtering again
                    const groupQuestions = group.question || [];

                    return (
                      <div key={`group-${group.id}`} className="mb-4">
                        <QuestionGroupItem
                          id={group.id}
                          title={group.title}
                          questions={groupQuestions}
                          subGroups={group.subGroups}
                          parentGroupId={group.parentGroupId}
                          nestingLevel={0}
                          onEditGroup={handleEditGroup}
                          onDeleteGroup={handleDeleteGroup}
                          onAddQuestionToGroup={handleAddQuestionToGroup}
                          onEditQuestion={(question) => {
                            setCurrentQuestion(question);
                            if (question.inputType === "table") {
                              setShowEditTableQuestionModal(true);
                            } else {
                              setShowEditQuestionModal(true);
                            }
                          }}
                          onDeleteQuestion={(question) => {
                            setCurrentQuestion(question);
                            setShowDeleteConfirmationModal(true);
                          }}
                          onDuplicateQuestion={(question) => {
                            setCurrentQuestion(question);
                            duplicateQuestionMutation.mutate({
                              id: question.id,
                              contextType,
                              contextId,
                            });
                          }}
                          onReorderQuestions={(questionPositions) => {
                            updatePositionsMutation.mutate({
                              contextType,
                              contextId,
                              questionPositions,
                            });
                          }}
                          onMoveQuestionBetweenGroups={handleMoveQuestionBetweenGroups}
                          onMoveGroupInsideGroup={handleMoveGroupInsideGroup}
                          isEditing={isEditingGroupName === group.id}
                          onStartEditing={startEditingGroupName}
                          onSaveGroupName={saveGroupName}
                          onCancelEditing={cancelEditingGroupName}
                          editingName={editingGroupName}
                          onEditingNameChange={setEditingGroupName}
                          selectionMode={selectionMode}
                          isDraggable={true}
                          selectedQuestionIds={selectedQuestionIds}
                          onToggleQuestionSelect={toggleQuestionSelection}
                          onCreateSubgroup={handleCreateSubgroup}
                        />
                      </div>
                    );
                  } else {
                    const question = item.data as Question;
                    return (
                      <div key={`question-${question.id}`} className="mb-4">
                        <QuestionItem
                          question={question}
                          onEdit={() => {
                            setCurrentQuestion(question);
                            if (question.inputType === "table") {
                              setShowEditTableQuestionModal(true);
                            } else {
                              setShowEditQuestionModal(true);
                            }
                          }}
                          onDelete={() => {
                            setCurrentQuestion(question);
                            setShowDeleteConfirmationModal(true);
                          }}
                          onDuplicate={() => {
                            setCurrentQuestion(question);
                            duplicateQuestionMutation.mutate({
                              id: question.id,
                              contextType,
                              contextId,
                            });
                          }}
                          selectionMode={selectionMode}
                          isSelected={selectedQuestionIds.includes(question.id)}
                          onToggleSelect={() =>
                            toggleQuestionSelection(question.id)
                          }
                        />
                      </div>
                    );
                  }
                })
              )}
            </div>
          </SortableContext>
        </DndContext>
      </div>

      {/* Subgroup name modal */}
      {showSubgroupNameModal && (
        <div className="fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-4">Create Subgroup</h3>
            <p className="text-sm text-neutral-600 mb-4">
              Creating a subgroup with {subgroupSelectedQuestions.length} selected question(s).
            </p>
            <div className="mb-4">
              <label htmlFor="subgroup-name" className="block text-sm font-medium mb-2">
                Subgroup Name
              </label>
              <input
                id="subgroup-name"
                type="text"
                value={subgroupName}
                onChange={(e) => setSubgroupName(e.target.value)}
                placeholder="Enter subgroup name"
                className="w-full px-3 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                autoFocus
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleSubgroupNameSubmit();
                  } else if (e.key === 'Escape') {
                    handleCancelSubgroup();
                  }
                }}
              />
            </div>
            <div className="flex justify-end gap-3">
              <button
                onClick={handleCancelSubgroup}
                className="px-4 py-2 text-neutral-600 hover:text-neutral-800 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSubgroupNameSubmit}
                disabled={!subgroupName.trim()}
                className="px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Create Subgroup
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Bottom action bar */}
      {questions.length > 0 && (
        <div className="sticky bottom-0 p-4 flex justify-center">
          <button
            className={`btn-primary  max-w-md flex items-center justify-center gap-2 ${
              !editFormPermission && "text-gray-400 cursor-not-allowed"
            }`}
            onClick={() => setShowAddQuestionModal(true)}
            disabled={!editFormPermission}
          >
            <PlusCircle size={16} />
            {t('addQuestion')}
          </button>
        </div>
      )}
    </div>
  );
};

export { FormBuilder };