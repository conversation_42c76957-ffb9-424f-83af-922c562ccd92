(()=>{var e={};e.id=7767,e.ids=[7767],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10125:(e,t,r)=>{"use strict";r.d(t,{Notification:()=>p});var s=r(60687),i=r(43210),n=r(54864),o=r(88920),a=r(57101),d=r(19150),l=r(14719),u=r(43649),c=r(93613);let p=()=>{let e=(0,n.wA)(),{message:t,type:r,visible:p}=(0,n.d4)(e=>e.notification);(0,i.useEffect)(()=>{if(p){let t=setTimeout(()=>{e((0,d._b)())},5e3);return()=>clearTimeout(t)}},[p,e]);let m="success"===r?(0,s.jsx)(l.A,{}):"warning"===r?(0,s.jsx)(u.A,{}):(0,s.jsx)(c.A,{});return(0,s.jsx)(o.N,{children:p&&(0,s.jsxs)(a.P.div,{className:`z-50 fixed top-0 right-0 m-4 px-4 py-2 rounded font-semibold w-auto max-w-xs flex items-center gap-2 cursor-pointer ${"success"===r?"bg-green-500 hover:bg-green-600":"warning"===r?"bg-yellow-500 hover:bg-yellow-600":"bg-red-500 hover:bg-red-600"} transition-colors duration-300`,onClick:()=>e((0,d._b)()),initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3,ease:"easeIn"},children:[(0,s.jsx)("span",{className:"text-2xl",children:m}),(0,s.jsx)("span",{className:"break-words neutral-100space-normal",children:t})]})})}},10271:(e,t,r)=>{"use strict";r.d(t,{ReactQueryProvider:()=>d});var s=r(60687),i=r(43210),n=r(39091),o=r(8693),a=r(9124);let d=({children:e})=>{let[t]=(0,i.useState)(()=>new n.E({defaultOptions:{queries:{staleTime:3e5,refetchOnWindowFocus:!1}}}));return(0,s.jsxs)(o.Ht,{client:t,children:[e,(0,s.jsx)(a.E,{initialIsOpen:!1})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12810:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let s=r(51060).A.create({baseURL:"http://localhost:4000/api",headers:{"Content-Type":"application/json"},withCredentials:!0});s.interceptors.request.use(e=>e,e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>("ERR_NETWORK"===e.code&&console.error("Network error - Please check if the backend server is running"),Promise.reject(e)));let i=s},14902:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>l});var s=r(65239),i=r(48088),n=r(88170),o=r.n(n),a=r(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l={children:["",{children:["edit-submission",{children:["[hashedId]",{children:["[submissionId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,95120)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\edit-submission\\[hashedId]\\[submissionId]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\edit-submission\\[hashedId]\\[submissionId]\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/edit-submission/[hashedId]/[submissionId]/page",pathname:"/edit-submission/[hashedId]/[submissionId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},16319:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19150:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>o,Ds:()=>i,_b:()=>n});let s=(0,r(9317).Z0)({name:"notification",initialState:{message:"",type:"",visible:!1},reducers:{showNotification:(e,t)=>{e.message=t.payload.message,e.type=t.payload.type,e.visible=!0},hideNotification:e=>{e.message="",e.type="",e.visible=!1}}}),{showNotification:i,hideNotification:n}=s.actions,o=s.reducer},21820:e=>{"use strict";e.exports=require("os")},26946:(e,t,r)=>{Promise.resolve().then(r.bind(r,10125)),Promise.resolve().then(r.bind(r,10271)),Promise.resolve().then(r.bind(r,49271))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31207:(e,t,r)=>{"use strict";r.d(t,{XV:()=>i,cZ:()=>a,ru:()=>n,yi:()=>s});let s=(e,t)=>{let r=new Map;e.forEach(e=>{let s=t.filter(t=>t.questionGroupId===e.id).sort((e,t)=>e.position-t.position);r.set(e.id,{...e,subGroups:[],question:s})});let s=[];return e.forEach(e=>{let t=r.get(e.id);if(e.parentGroupId){let s=r.get(e.parentGroupId);s&&(s.subGroups=s.subGroups||[],s.subGroups.push(t))}else s.push(t)}),s},i=(e,t)=>{let r=[];return e.forEach(e=>{let t=e=>[...e.question||[],...(e.subGroups||[]).flatMap(t)],s=t(e),i=s.length>0?Math.min(...s.map(e=>e.position)):e.order;r.push({type:"group",data:e,order:i,originalPosition:i})}),t.forEach(e=>{r.push({type:"question",data:e,order:e.position,originalPosition:e.position})}),r.sort((e,t)=>e.order===t.order?(e.originalPosition||e.order)-(t.originalPosition||t.order):e.order-t.order)},n=e=>e.filter(e=>null===e.questionGroupId||void 0===e.questionGroupId),o=e=>{let t=[];return e.forEach(e=>{t.push(e.id),e.subGroups&&e.subGroups.length>0&&t.push(...o(e.subGroups))}),t},a=(e,t=!0)=>{let r={};return o(e).forEach(e=>{r[e]=t}),r}},33873:e=>{"use strict";e.exports=require("path")},35790:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>o,l:()=>n,yg:()=>i});let s=(0,r(9317).Z0)({name:"createLibraryItem",initialState:{visible:!1},reducers:{showCreateLibraryModal:e=>{e.visible=!0},hideCreateLibraryModal:e=>{e.visible=!1}}}),{showCreateLibraryModal:i,hideCreateLibraryModal:n}=s.actions,o=s.reducer},42895:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>d,Le:()=>o,jB:()=>a,tQ:()=>i,x9:()=>n});let s=(0,r(9317).Z0)({name:"auth",initialState:{status:"loading",user:null,error:null},reducers:{setAuthenticatedUser:(e,t)=>{e.status="authenticated",e.user=t.payload,e.error=null},setUnauthenticated:e=>{e.status="unauthenticated",e.user=null,e.error=null},setAuthLoading:e=>{e.status="loading"},setAuthError:(e,t)=>{e.status="unauthenticated",e.error=t.payload,e.user=null}}}),{setAuthenticatedUser:i,setUnauthenticated:n,setAuthLoading:o,setAuthError:a}=s.actions,d=s.reducer},44305:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(60687),i=r(43210),n=r(78272),o=r(14952),a=r(69396);let d=({group:e,nestingLevel:t=0,visibleQuestions:r,nestedQuestions:l,renderQuestionInput:u,errors:c,onToggleExpansion:p,isExpanded:m,expandedGroups:h,className:b=""})=>{let[x,f]=(0,i.useState)(!0),y=void 0!==m?m:x,v=e.question||[],g=v.filter(e=>r.some(t=>t.id===e.id)),q=(e.subGroups||[]).filter(e=>(e.question||[]).some(e=>r.some(t=>t.id===e.id)));return 0===g.length&&0===q.length?null:(0,s.jsxs)("div",{className:`border border-neutral-400 rounded-md bg-card shadow-sm mb-4 ${t>0?"ml-8 border-l-4 border-l-primary-300":""} ${b}`,children:[(0,s.jsx)("div",{className:"flex items-center justify-between p-4 bg-neutral-100 border-b border-neutral-300 rounded-t-md cursor-pointer hover:bg-neutral-200 dark:bg-gray-700 dark:border-gray-600 dark:hover:bg-gray-600",onClick:()=>{p?p(e.id):f(!x)},children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[y?(0,s.jsx)(n.A,{className:"h-5 w-5 text-neutral-700 dark:text-neutral-300"}):(0,s.jsx)(o.A,{className:"h-5 w-5 text-neutral-700 dark:text-neutral-300"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-neutral-900 dark:text-neutral-100",children:e.title}),(0,s.jsxs)("span",{className:"text-sm text-neutral-700 dark:text-neutral-400",children:["(",g.length+q.reduce((e,t)=>e+(t.question?.length||0),0)," visible question",g.length+q.reduce((e,t)=>e+(t.question?.length||0),0)!==1?"s":"",")"]})]})}),y&&(0,s.jsxs)("div",{className:"p-4 space-y-4",children:[q.sort((e,t)=>e.order-t.order).map(e=>{let i=h?h[e.id]:void 0;return(0,s.jsx)(d,{group:e,nestingLevel:t+1,visibleQuestions:r,nestedQuestions:l,renderQuestionInput:u,errors:c,onToggleExpansion:p,isExpanded:i,expandedGroups:h,className:b},e.id)}),l.filter(e=>v.some(t=>t.id===e.question.id)).map(e=>(0,s.jsx)(a.A,{questionGroup:e,renderQuestionInput:u,errors:c,className:""},e.question.id))]})]})},l=d},44395:(e,t,r)=>{"use strict";r.d(t,{Notification:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Notification() from the server but Notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\components\\general\\Notification.tsx","Notification")},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},49271:(e,t,r)=>{"use strict";r.d(t,{ReduxProvider:()=>p});var s=r(60687),i=r(9317),n=r(19150),o=r(58432),a=r(42895),d=r(35790),l=r(89011);let u=(0,i.U1)({reducer:{notification:n.Ay,createProject:o.Ay,auth:a.Ay,createLibrary:d.Ay,createLibraryItem:l.Ay}});r(43210);var c=r(54864);let p=({children:e})=>(0,s.jsx)(c.Kq,{store:u,children:e})},50823:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},54481:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>k});var s=r(60687),i=r(16189),n=r(29494),o=r(12810),a=r(6986),d=r(75531),l=r(86429),u=r(43210),c=r(39390),p=r(15616),m=r(93437),h=r(40347),b=r(70334),x=r(54050),f=r(54864),y=r(19150),v=r(70332),g=r(80967),q=r(96),j=r(71845),w=r(24527),I=r(69396),N=r(44305),P=r(31207);function O({questions:e,submission:t,projectId:r,submissionId:i,onClose:a,onSave:d}){let l=(0,f.wA)(),[O,E]=(0,u.useState)({}),[k,C]=(0,u.useState)({}),[S,A]=(0,u.useState)({}),[R,T]=(0,u.useState)(!1),[G,M]=(0,u.useState)([]),[$,_]=(0,u.useState)([]),[Q,D]=(0,u.useState)({}),[K,L]=(0,u.useState)(new Set);(0,u.useRef)(new Set),(0,u.useRef)(!1),(0,u.useRef)("");let{data:U=[]}=(0,n.I)({queryKey:["questionGroups",r],queryFn:()=>(0,q.pr)({projectId:r}),enabled:!!r}),{data:F}=(0,n.I)({queryKey:["project",r],queryFn:()=>(0,j.kf)({projectId:r}),enabled:!!r}),J=(0,u.useMemo)(()=>(0,P.yi)(U,e),[U,e]),z=(0,u.useMemo)(()=>(0,P.ru)(e),[e]),Z=(0,u.useMemo)(()=>(0,P.XV)(J,z),[J,z]),Y=(0,u.useCallback)(e=>{D(t=>({...t,[e]:!t[e]}))},[]),H=(0,u.useCallback)((e,t)=>{L(t=>new Set(t).add(e)),E(r=>({...r,[e]:t})),A(t=>({...t,[e]:""}))},[]),V=()=>{let e=(0,w.WK)(G,O);return A(e),0===Object.keys(e).length},W=(0,x.n)({mutationFn:async s=>{let n=e.map(e=>{let n=s[e.id],o="selectmany"===e.inputType,a=t.answers.find(t=>t.question.id===e.id),d=!a?.id;if(o&&Array.isArray(n)){if(n.length>0){let t=[];e.questionOptions&&(t=n.map(t=>{let r=e.questionOptions.find(e=>e.label===t);return r?.id}).filter(e=>void 0!==e));let s={projectId:r,questionId:e.id,answerType:e.inputType,value:n.join(", "),questionOptionId:t,isOtherOption:!1,formSubmissionId:i};return d?s:{...s,id:a.id}}return null}{let t,s;if(void 0===(t="number"===e.inputType||"decimal"===e.inputType?n?Number(n):void 0:"date"===e.inputType||"dateandtime"===e.inputType?n||void 0:"table"===e.inputType?Array.isArray(n)&&n.length>0?JSON.stringify(n):void 0:n?String(n):void 0))return null;if("selectone"===e.inputType&&n&&e.questionOptions){let t=e.questionOptions.find(e=>e.label===n);s=t?.id}let o={projectId:r,questionId:e.id,answerType:e.inputType,value:t,questionOptionId:s,isOtherOption:!1,formSubmissionId:i};return d?o:{...o,id:a.id}}}).filter(e=>null!==e);if(0===n.length)throw Error("No valid answers with IDs to submit");let a=n.map(e=>e.id?{id:e.id,questionId:e.questionId,projectId:r,value:e.value,answerType:e.answerType,questionOptionId:e.questionOptionId,isOtherOption:e.isOtherOption||!1,formSubmissionId:e.formSubmissionId}:e.questionId?{questionId:e.questionId,projectId:r,value:e.value,answerType:e.answerType,questionOptionId:e.questionOptionId,isOtherOption:e.isOtherOption||!1,formSubmissionId:e.formSubmissionId}:null).filter(e=>null!==e);try{return await (0,g.GN)(a,r)}catch(s){console.error("Error with /answers/multiple endpoint:",s),s.response&&(console.error("Error response data:",JSON.stringify(s.response.data,null,2)),console.error("Error response status:",s.response.status),console.error("Error response headers:",s.response.headers));let e=[],t=[];for(let s of n)try{if(s.id){let{data:t}=await o.A.patch(`/answers/${s.id}?projectId=${r}`,{id:s.id,questionId:s.questionId,projectId:r,value:s.value,answerType:s.answerType,questionOptionId:s.questionOptionId,isOtherOption:s.isOtherOption||!1,formSubmissionId:s.formSubmissionId});e.push(t)}else if(s.questionId){let{data:t}=await o.A.post(`/answers?projectId=${r}`,{submissionId:s.formSubmissionId,questionId:s.questionId,value:s.value,answerType:s.answerType,questionOptionId:s.questionOptionId,isOtherOption:s.isOtherOption||!1});e.push(t)}}catch(r){let e=s.id||s.questionId;console.error(`Error handling answer ${e}:`,r),r.response&&console.error("Individual error response data:",JSON.stringify(r.response.data,null,2)),t.push(e)}if(t.length>0)throw Error(`Failed to update answers with IDs: ${t.join(", ")}`);if(e.length>0)return l((0,y.Ds)({message:"Submission updated successfully using individual updates. Consider checking the bulk update endpoint.",type:"warning"})),e;throw s}},onSuccess:()=>{l((0,y.Ds)({message:"Submission updated successfully. You can continue editing if needed.",type:"success"})),L(new Set),d()},onError:e=>{let t=e.response?.data?.message||e.response?.data?.error||e.message||"Failed to update submission. Please check your input and try again.";l((0,y.Ds)({message:t,type:"error"})),console.error("Update Error:",{message:t,status:e.response?.status,data:JSON.stringify(e.response?.data,null,2)})},onSettled:()=>{T(!1)}}),X=async e=>{e.preventDefault(),V()&&(T(!0),W.mutate(O))},B=t=>e.some(e=>e.questionOptions?.some(e=>e.nextQuestionId===t)),ee=e=>e.questionOptions?.some(e=>e.nextQuestionId)||!1,et=e=>{let t=B(e.id),r=ee(e);return(0,s.jsxs)("div",{className:`border rounded-md p-4 ${t?"border-primary-200 dark:border-primary-700 bg-primary-100 dark:bg-primary-900/20":"border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"}`,children:[(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(c.J,{className:"text-base font-medium",children:[e.label,e.isRequired&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),t&&(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200",children:[(0,s.jsx)(b.A,{className:"w-3 h-3 mr-1"}),"Follow-up"]}),r&&(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-200 text-accent-700 dark:bg-accent-700/20 dark:text-accent-200",children:"Has conditions"})]}),e.hint&&(0,s.jsx)("p",{className:`text-sm mt-1 ${t?"text-primary-700 dark:text-primary-300":"text-muted-foreground"}`,children:e.hint}),S[e.id]&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:S[e.id]})]}),(0,s.jsx)("div",{className:"mt-2",children:er(e)})]},e.id)},er=e=>{let t=O[e.id]??("selectmany"===e.inputType?[]:"");switch(e.inputType){case"text":if(e.hint?.includes("multiline"))return(0,s.jsx)(p.T,{value:t,onChange:t=>H(e.id,t.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});return(0,s.jsx)("input",{className:"input-field w-full",value:t,onChange:t=>H(e.id,t.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"number":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",value:t,onChange:t=>H(e.id,t.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"decimal":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",step:"any",value:t,onChange:t=>H(e.id,t.target.value),placeholder:e.hint||"Your answer",required:e.isRequired});case"selectone":return(0,s.jsx)(h.z,{value:t,onValueChange:t=>H(e.id,t),required:e.isRequired,children:(0,s.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(h.C,{value:e.label,id:`option-${e.id}`}),(0,s.jsx)(c.J,{htmlFor:`option-${e.id}`,className:"cursor-pointer",children:e.label})]},t))})});case"selectmany":return(0,s.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map(r=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(m.S,{id:`option-${r.id}`,checked:(t||[]).includes(r.label),onCheckedChange:s=>{let i=t||[],n=s?[...i,r.label]:i.filter(e=>e!==r.label);H(e.id,n)}}),(0,s.jsx)(c.J,{htmlFor:`option-${r.id}`,className:"cursor-pointer",children:r.label})]},r.id))});case"date":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"date",value:t,onChange:t=>H(e.id,t.target.value),placeholder:e.hint||"Select date",required:e.isRequired})});case"dateandtime":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"time",value:t,onChange:t=>H(e.id,t.target.value),placeholder:e.hint||"Select time",required:e.isRequired})});case"table":return(0,s.jsx)(v.N,{questionId:e.id,value:t,onChange:t=>H(e.id,t),required:e.isRequired,tableLabel:e.label});default:return null}};return(0,s.jsxs)("div",{className:"w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("h2",{className:"text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700",children:["Edit Submission",F?.name?` for ${F.name}`:""]}),(0,s.jsx)("form",{onSubmit:X,className:"p-6",children:(0,s.jsxs)("div",{className:"space-y-6",children:[0===e.length?(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}):Z.map(e=>{if("group"===e.type){let t=e.data,r=Q[t.id];return(0,s.jsx)(N.A,{group:t,nestingLevel:0,visibleQuestions:G,nestedQuestions:$,renderQuestionInput:er,errors:S,onToggleExpansion:Y,isExpanded:r,expandedGroups:Q,className:""},`group-${t.id}`)}{let t=e.data;if(!G.some(e=>e.id===t.id))return null;let r=$.find(e=>e.question.id===t.id);return r?(0,s.jsx)(I.A,{questionGroup:r,renderQuestionInput:er,errors:S,className:""},t.id):et(t)}}),e.length>0&&(0,s.jsxs)("div",{className:"mt-6 flex justify-end gap-4",children:[(0,s.jsx)("button",{className:"btn-primary bg-neutral-500 hover:bg-neutral-600",type:"button",onClick:a,disabled:R,children:"Cancel"}),(0,s.jsx)("button",{className:"btn-primary",type:"submit",disabled:R,children:R?"Saving...":"Save Changes"})]})]})})]})}let E=async(e,t)=>{let{data:r}=await o.A.get(`/form-submissions/${e}`),s=r.data.formSubmissions.find(e=>e.id===t);if(!s)throw Error("Submission not found");return s};function k(){let{hashedId:e,submissionId:t}=(0,i.useParams)(),r=(0,a.D)(e),o=Number(t);if(null===r||isNaN(o))return(0,s.jsx)("div",{children:"Error: Invalid project or submission ID."});let{data:u=[],isLoading:c,isError:p}=(0,n.I)({queryKey:["questions",r],queryFn:()=>(0,d.K4)({projectId:r}),enabled:!!r}),{data:m,isLoading:h,isError:b,refetch:x}=(0,n.I)({queryKey:["submission",r,o],queryFn:()=>E(r,o),enabled:!!r&&!!o});return c||h?(0,s.jsx)(l.A,{}):p||b||!u||!m?(0,s.jsx)("p",{className:"text-sm text-red-500",children:"Error loading submission or form. Please try again."}):(0,s.jsx)("div",{className:"min-h-screen w-full bg-neutral-100 dark:bg-gray-900 flex flex-col items-center p-4 md:p-6",children:(0,s.jsx)(O,{questions:u,submission:m,projectId:r,submissionId:o,onSave:()=>{window.opener&&window.opener.postMessage({type:"REFETCH_SUBMISSIONS"},"*"),x()}})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,metadata:()=>c});var s=r(37413);r(82704);var i=r(7990),n=r.n(i),o=r(60866),a=r.n(o),d=r(77832),l=r(44395),u=r(60265);let c={title:"Data analysis tool",description:"A tool for data collection and analysis."};function p({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${n().className} ${a().className} antialiased`,children:(0,s.jsx)(d.ReduxProvider,{children:(0,s.jsxs)(u.ReactQueryProvider,{children:[(0,s.jsx)(l.Notification,{}),(0,s.jsx)("main",{className:"bg-neutral-200",children:e})]})})})})}},58432:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>o,Gl:()=>i,th:()=>n});let s=(0,r(9317).Z0)({name:"createProject",initialState:{visible:!1},reducers:{showCreateProjectModal:e=>{e.visible=!0},hideCreateProjectModal:e=>{e.visible=!1}}}),{showCreateProjectModal:i,hideCreateProjectModal:n}=s.actions,o=s.reducer},60265:(e,t,r)=>{"use strict";r.d(t,{ReactQueryProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReactQueryProvider.tsx","ReactQueryProvider")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64702:(e,t,r)=>{Promise.resolve().then(r.bind(r,54481))},69430:(e,t,r)=>{Promise.resolve().then(r.bind(r,95120))},74075:e=>{"use strict";e.exports=require("zlib")},77832:(e,t,r)=>{"use strict";r.d(t,{ReduxProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ReduxProvider() from the server but ReduxProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\providers\\ReduxProvider.tsx","ReduxProvider")},79551:e=>{"use strict";e.exports=require("url")},80967:(e,t,r)=>{"use strict";r.d(t,{GN:()=>a,J6:()=>n,O8:()=>i,s4:()=>o});var s=r(12810);let i=async(e,t)=>{try{let{data:r}=await s.A.delete(`/form-submissions/${e}?projectId=${t}`);return r}catch(e){throw console.error("Error deleting form submission:",e),e}},n=async(e,t)=>{try{let r=e.map(e=>s.A.delete(`/form-submissions/${e}?projectId=${t}`));return(await Promise.all(r)).map(e=>e.data)}catch(e){throw console.error("Error deleting multiple form submissions:",e),e}},o=async(e,t)=>{try{if(!e.submissionId||!e.questionId)throw Error("submissionId and questionId are required");let r={...e};null===r.questionOptionId?delete r.questionOptionId:Array.isArray(r.questionOptionId)&&(r.questionOptionId=r.questionOptionId.filter(e=>null!=e),0===r.questionOptionId.length&&delete r.questionOptionId);let{data:i}=await s.A.patch(`/answers/${e.questionId}?projectId=${t}`,r);return i}catch(e){throw console.error("Error updating answer:",e),e}},a=async(e,t)=>{try{let{data:r}=await s.A.patch(`/answers/multiple?projectId=${t}`,e);return r}catch(e){throw console.error("Error updating multiple answers with endpoint:",e),e}}},81630:e=>{"use strict";e.exports=require("http")},82704:()=>{},83997:e=>{"use strict";e.exports=require("tty")},86778:(e,t,r)=>{Promise.resolve().then(r.bind(r,44395)),Promise.resolve().then(r.bind(r,60265)),Promise.resolve().then(r.bind(r,77832))},89011:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>o,dQ:()=>i,g7:()=>n});let s=(0,r(9317).Z0)({initialState:{visible:!1,option:""},name:"createLibraryItem",reducers:{showCreateLibraryItemModal:(e,t)=>{e.visible=!0,e.option=t.payload},hideCreateLibraryItemModal:e=>{e.visible=!1,e.option=""}}}),{showCreateLibraryItemModal:i,hideCreateLibraryItemModal:n}=s.actions,o=s.reducer},94735:e=>{"use strict";e.exports=require("events")},95120:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\edit-submission\\\\[hashedId]\\\\[submissionId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\edit-submission\\[hashedId]\\[submissionId]\\page.tsx","default")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,7404,1658,6560,3851,9168,2060],()=>r(14902));module.exports=s})();