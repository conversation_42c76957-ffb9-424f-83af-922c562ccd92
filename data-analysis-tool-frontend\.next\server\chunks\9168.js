"use strict";exports.id=9168,exports.ids=[9168],exports.modules={14952:(e,t,s)=>{s.d(t,{A:()=>r});let r=(0,s(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},24527:(e,t,s)=>{s.d(t,{OD:()=>o,Tr:()=>d,UL:()=>i,WK:()=>m});let r=(e,t)=>{if(!e.questionOptions||0===e.questionOptions.length)return null;if("selectone"===e.inputType&&"string"==typeof t){let s=e.questionOptions.find(e=>e.label===t);return s?.nextQuestionId||null}if("selectmany"===e.inputType&&Array.isArray(t))for(let s of t){let t=e.questionOptions.find(e=>e.label===s);if(t?.nextQuestionId)return t.nextQuestionId}return null},l=e=>e.questionOptions&&0!==e.questionOptions.length?e.questionOptions.map(e=>e.nextQuestionId).filter(e=>null!=e):[],i=(e,t)=>{let s=new Set,i=new Set;return e.forEach(e=>{l(e).forEach(e=>i.add(e))}),e.forEach(e=>{i.has(e.id)||s.add(e.id)}),Object.entries(t).forEach(([t,l])=>{let i=parseInt(t),n=e.find(e=>e.id===i);if(n&&l){let e=r(n,l);e&&s.add(e)}}),e.filter(e=>s.has(e.id))},n=(e,t)=>{let s=t.find(t=>t.id===e);if(!s)return[];let r=l(s);return t.filter(e=>r.includes(e.id))},a=(e,t)=>t.some(t=>l(t).includes(e)),d=(e,t)=>{let s=new Set(i(e,t).map(e=>e.id));return e.filter(t=>!a(t.id,e)).sort((e,t)=>e.position-t.position).map(t=>{let r=n(t.id,e).sort((e,t)=>e.position-t.position);return{question:t,isVisible:s.has(t.id),isFollowUp:!1,followUps:r.map(e=>({question:e,isVisible:s.has(e.id)}))}}).filter(e=>e.isVisible||e.followUps.some(e=>e.isVisible))},o=(e,t)=>{let s=new Set(t.map(e=>e.id)),r={};return Object.entries(e).forEach(([e,t])=>{s.has(parseInt(e))&&(r[e]=t)}),r},m=(e,t)=>{let s={};return e.forEach(e=>{if(e.isRequired){let r=t[e.id];("string"==typeof r&&!r.trim()||Array.isArray(r)&&0===r.length||null==r)&&(s[e.id]=`${e.label} is required`)}}),s}},69396:(e,t,s)=>{s.d(t,{A:()=>i});var r=s(60687);s(43210);var l=s(39390);let i=({questionGroup:e,renderQuestionInput:t,errors:s,className:i=""})=>{let{question:n,isVisible:a,followUps:d}=e;return a||d.some(e=>e.isVisible)?(0,r.jsxs)("div",{className:`${i}`,children:[a&&(0,r.jsxs)("div",{className:"border border-neutral-500 dark:border-neutral-700 rounded-md p-4 bg-neutral-100 dark:bg-neutral-800",children:[(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsxs)(l.J,{className:"text-base font-medium",children:[n.label,n.isRequired&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),n.hint&&(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:n.hint}),s[n.id]&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:s[n.id]})]}),(0,r.jsx)("div",{className:"mt-2",children:t(n)}),d.some(e=>e.isVisible)&&(0,r.jsx)("div",{className:"mt-4 ml-4 space-y-3 border-l-2 border-primary-200 dark:border-primary-700 pl-4",children:d.map(({question:e,isVisible:i})=>i&&(0,r.jsxs)("div",{className:"border border-neutral-100 dark:border-neutral-600 rounded-md p-3 bg-primary-50 dark:bg-primary-900/20",children:[(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsxs)(l.J,{className:"text-sm font-medium text-primary-900 dark:text-primary-100",children:[e.label,e.isRequired&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),e.hint&&(0,r.jsx)("p",{className:"text-xs text-primary-700 dark:text-primary-300 mt-1",children:e.hint}),s[e.id]&&(0,r.jsx)("p",{className:"text-xs text-red-500 mt-1",children:s[e.id]})]}),(0,r.jsx)("div",{className:"mt-2",children:t(e)})]},e.id))})]}),!a&&d.some(e=>e.isVisible)&&(0,r.jsx)("div",{className:"space-y-3",children:d.map(({question:e,isVisible:i})=>i&&(0,r.jsxs)("div",{className:"border border-neutral-200 dark:border-neutral-700 rounded-md p-4 bg-white dark:bg-neutral-800",children:[(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsxs)(l.J,{className:"text-base font-medium",children:[e.label,e.isRequired&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),e.hint&&(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.hint}),s[e.id]&&(0,r.jsx)("p",{className:"text-sm text-red-500 mt-1",children:s[e.id]})]}),(0,r.jsx)("div",{className:"mt-2",children:t(e)})]},e.id))})]}):null}},70332:(e,t,s)=>{s.d(t,{N:()=>d});var r=s(60687),l=s(43210),i=s.n(l),n=s(96752),a=s(68988);function d({questionId:e,value:t,onChange:s,required:d=!1,tableLabel:o}){let[m,u]=(0,l.useState)([]),[c,p]=(0,l.useState)([]),[h,x]=(0,l.useState)({}),[b,j]=(0,l.useState)(!0),[f,g]=(0,l.useState)(null),[N,v]=(0,l.useState)({}),q=i().useMemo(()=>{if(0===m.length)return{parentColumns:[],columnMap:new Map,hasChildColumns:!1};let e=m.filter(e=>void 0===e.parentColumnId||null===e.parentColumnId),t=new Map;e.forEach(e=>{let s=m.filter(t=>t.parentColumnId===e.id);t.set(e.id,s)});let s=e.some(e=>(t.get(e.id)||[]).length>0);return{parentColumns:e,columnMap:t,hasChildColumns:s}},[m]),y=(e,t,r)=>{let l=`${e}_${t}`;x(e=>({...e,[l]:r})),setTimeout(()=>{let e={...h,[l]:r},t=[];Object.entries(e).forEach(([e,s])=>{if(""!==s.trim()){let[r,l]=e.split("_").map(Number);t.push({columnId:r,rowsId:l,value:s})}}),s(t)},0)},w=0===m.length;return(0,r.jsx)("div",{className:"overflow-x-auto",children:b?(0,r.jsx)("div",{className:"py-4 text-center",children:"Loading table..."}):f?(0,r.jsx)("div",{className:"py-4 text-center text-red-500",children:f}):w?(0,r.jsx)("div",{className:"py-4 text-center text-amber-600",children:"This table has no columns defined. Please configure the table question first."}):(0,r.jsxs)(n.XI,{className:"border-collapse",children:[(0,r.jsxs)(n.A0,{children:[(0,r.jsx)(n.Hj,{children:q.parentColumns.map(e=>{let t=(q.columnMap.get(e.id)||[]).length||1;return(0,r.jsx)(n.nd,{colSpan:t,className:"text-center border bg-blue-50 font-medium",children:e.columnName},e.id)})}),q.hasChildColumns&&(0,r.jsx)(n.Hj,{children:q.parentColumns.map(e=>{let t=q.columnMap.get(e.id)||[];return 0===t.length?(0,r.jsx)(n.nd,{className:"border bg-blue-50/50 text-sm"},`empty-${e.id}`):t.map(e=>(0,r.jsx)(n.nd,{className:"border bg-blue-50/50 text-sm",children:e.columnName},e.id))})})]}),(0,r.jsx)(n.BF,{children:c.length>0?c.map((e,t)=>(0,r.jsx)(n.Hj,{className:t%2==0?"bg-white":"bg-gray-50",children:q.parentColumns.map(t=>{let s=q.columnMap.get(t.id)||[];return 0===s.length?(0,r.jsx)(n.nA,{className:"border p-1",children:(0,r.jsx)(a.p,{value:h[`${t.id}_${e.id}`]||"",onChange:s=>y(t.id,e.id,s.target.value),className:"w-full",required:d,placeholder:"Enter value"})},`cell-${t.id}-${e.id}`):s.map(t=>(0,r.jsx)(n.nA,{className:"border p-1",children:(0,r.jsx)(a.p,{value:h[`${t.id}_${e.id}`]||"",onChange:s=>y(t.id,e.id,s.target.value),className:"w-full",required:d,placeholder:"Enter value"})},`cell-${t.id}-${e.id}`))})},e.id)):(0,r.jsx)(n.Hj,{children:q.parentColumns.map(e=>{let t=q.columnMap.get(e.id)||[];return 0===t.length?(0,r.jsx)(n.nA,{className:"border p-1",children:(0,r.jsx)(a.p,{value:h[`${e.id}_no_row`]||"",onChange:t=>y(e.id,"no_row",t.target.value),className:"w-full",required:d,placeholder:"Enter value"})},`cell-${e.id}-no-row`):t.map(e=>(0,r.jsx)(n.nA,{className:"border p-1",children:(0,r.jsx)(a.p,{value:h[`${e.id}_no_row`]||"",onChange:t=>y(e.id,"no_row",t.target.value),className:"w-full",required:d,placeholder:"Enter value"})},`cell-${e.id}-no-row`))})})})]})})}s(12810)},75531:(e,t,s)=>{s.d(t,{K4:()=>l,dI:()=>n,ej:()=>i});var r=s(12810);let l=async({projectId:e})=>{let{data:t}=await r.A.get(`/questions/${e}`);return t.questions},i=async({templateId:e})=>{let{data:t}=await r.A.get(`/template-questions/${e}`);return t.questions},n=async()=>{try{return(await r.A.get("/question-blocks")).data.questions||[]}catch(e){throw console.error("Error fetching question block questions:",e),e}}}};