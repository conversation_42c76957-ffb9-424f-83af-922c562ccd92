import Decimal from 'decimal.js';
import { RawNumberFormatResult, UnsignedRoundingModeType } from '../types/number';
/**
 * https://tc39.es/ecma402/#sec-torawfixed
 * @param x a finite non-negative Number or BigInt
 * @param minFraction an integer between 0 and 20
 * @param maxFraction an integer between 0 and 20
 */
export declare function ToRawFixed(x: Decimal, minFraction: number, maxFraction: number, roundingIncrement: number, unsignedRoundingMode: UnsignedRoundingModeType): RawNumberFormatResult;
