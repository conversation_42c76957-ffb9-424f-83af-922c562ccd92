(()=>{var e={};e.id=7969,e.ids=[7969],e.modules={2828:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var r=t(60687),i=t(3204),a=t(19150),n=t(63442),l=t(38038),o=t(12597),d=t(13861),c=t(16189),p=t(43210),m=t(27605),u=t(54864),x=t(45880),h=t(21650),f=t(86429),b=t(77618);let v=()=>{let e=(0,b.c3)(),s=x.z.object({email:x.z.string().min(1,e("emailRequired")).email(e("invalidEmail")),password:x.z.string().min(1,e("passwordRequired"))}),{register:t,formState:{errors:v,isSubmitting:j},handleSubmit:g,getValues:y,watch:w}=(0,m.mN)({resolver:(0,n.u)(s)}),N=w("password"),A=(0,c.useRouter)(),k=(0,u.wA)(),{hashedId:P}=(0,c.useParams)(),q=`/form-submission/${P}`,[C,E]=(0,p.useState)(!1),[I,_]=(0,p.useState)(!1),{signin:z,isAuthenticated:M,isLoading:S}=(0,h.A)();(0,p.useEffect)(()=>{!S&&M&&A.push(q)},[S,M,A,q]);let D=async s=>{z({email:s.email,password:s.password},()=>{k((0,a.Ds)({message:e("signInSuccessful"),type:"success"})),A.push(q)},s=>{"unverified"===s?E(!0):k((0,a.Ds)({message:e("invalidEmailOrPassword"),type:"error"}))})};return S?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)(f.A,{})}):M?null:(0,r.jsxs)("div",{className:"min-h-screen flex items-center justify-center",children:[(0,r.jsx)(i.x,{email:y("email"),showModal:C,setShowModal:E}),(0,r.jsxs)("div",{className:"flex flex-col section w-11/12 mobile:w-4/5 tablet:w-lg",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2 mb-8",children:[(0,r.jsx)(l.A,{size:36}),(0,r.jsx)("h1",{className:"text-2xl tablet:text-3xl font-semibold text-center",children:e("signInToAccessForm")}),(0,r.jsx)("p",{className:"text-neutral-700 text-center",children:e("loginToCompleteForm")})]}),(0,r.jsxs)("form",{className:"flex flex-col gap-4 mb-4",onSubmit:g(D),children:[(0,r.jsxs)("div",{className:"group label-input-group",children:[(0,r.jsx)("label",{htmlFor:"email",className:"label-text",children:e("email")}),(0,r.jsx)("input",{...t("email"),id:"email",type:"email",placeholder:e("enterEmail"),className:"input-field"}),v.email&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${v.email.message}`})]}),(0,r.jsxs)("div",{className:"group label-input-group",children:[(0,r.jsx)("label",{htmlFor:"password",className:"label-text",children:e("password")}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{...t("password"),id:"password",type:I?"text":"password",placeholder:e("enterPassword"),className:"input-field w-full pr-10"}),N&&N.length>0&&(0,r.jsxs)("button",{type:"button",tabIndex:-1,className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>_(!I),children:[I?(0,r.jsx)(o.A,{className:"h-4 w-4"}):(0,r.jsx)(d.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{className:"sr-only",children:[I?"Hide":"Show"," password"]})]})]}),v.password&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:`${v.password.message}`})]}),(0,r.jsx)("button",{type:"submit",className:"btn-primary",disabled:j,children:j?(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[e("signingIn"),(0,r.jsx)("div",{className:"size-4 rounded-full border-x-2 animate-spin"})]}):e("submit")})]})]})]})}},3204:(e,s,t)=>{"use strict";t.d(s,{x:()=>m});var r=t(60687),i=t(43210),a=t(38587),n=t(19169),l=t(43649),o=t(12810),d=t(54864),c=t(19150),p=t(77618);let m=({email:e,showModal:s,setShowModal:t})=>{let m=(0,d.wA)(),u=(0,p.c3)(),x=async()=>{try{await o.A.post("/users/sendverificationemail",{email:e})}catch(e){m((0,c.Ds)({message:u("failedToSendVerification"),type:"error"}))}},[h,f]=(0,i.useState)(!0),[b,v]=(0,i.useState)(60);(0,i.useEffect)(()=>{let e;return h&&b>0?e=window.setInterval(()=>{v(e=>e-1)},1e3):0===b&&(f(!1),v(60)),()=>clearInterval(e)},[h,b]),(0,i.useEffect)(()=>(s&&e&&x(),()=>{v(60),f(!0)}),[s]);let j=async()=>{f(!0);try{await o.A.post("/users/sendverificationemail",{email:e})}catch(e){console.error("error sending email",e)}};return(0,r.jsxs)(a.A,{isOpen:s,onClose:()=>t(!1),className:"flex flex-col items-center gap-4",children:[(0,r.jsx)("div",{className:"rounded-full p-2 bg-primary-300",children:(0,r.jsx)(n.A,{className:"text-primary-500"})}),(0,r.jsx)("h1",{className:"heading-text",children:u("checkYourEmail")}),(0,r.jsxs)("p",{className:"flex flex-col items-center",children:[u("verificationSentTo"),(0,r.jsx)("span",{className:"font-medium",children:e})]}),(0,r.jsxs)("div",{className:"flex gap-2 items-center bg-yellow-100 text-yellow-900 px-4 py-2 rounded-md",children:[(0,r.jsx)(l.A,{size:16})," ",u("didNotReceiveEmail")]}),(0,r.jsx)("button",{className:"btn-primary",onClick:j,disabled:h,children:h?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("span",{children:[u("resendIn")," ",b,"s"]}),(0,r.jsx)("div",{className:"size-4 animate-spin border-x-2 rounded-full"})]}):(0,r.jsx)("span",{children:u("resend")})})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19169:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},21820:e=>{"use strict";e.exports=require("os")},25390:(e,s,t)=>{Promise.resolve().then(t.bind(t,2828))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30844:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=t(65239),i=t(48088),a=t(88170),n=t.n(a),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d={children:["",{children:["form-submission",{children:["[hashedId]",{children:["sign-in",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,58918)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\form-submission\\[hashedId]\\sign-in\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\form-submission\\[hashedId]\\sign-in\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/form-submission/[hashedId]/sign-in/page",pathname:"/form-submission/[hashedId]/sign-in",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33873:e=>{"use strict";e.exports=require("path")},38587:(e,s,t)=>{"use strict";t.d(s,{A:()=>o});var r=t(60687),i=t(88920),a=t(57101),n=t(74699),l=t(11860);t(43210);let o=({children:e,className:s,isOpen:t,onClose:o,preventOutsideClick:d=!1})=>(0,r.jsx)(i.N,{children:t&&(0,r.jsx)(a.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto",onClick:e=>{d||o()},children:(0,r.jsxs)(a.P.div,{initial:{scale:.6,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.6,opacity:0},transition:{duration:.3,ease:n.am},className:`relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ${s}`,onClick:e=>e.stopPropagation(),children:[(0,r.jsx)(l.A,{onClick:o,className:"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300"}),e]})})})},48358:(e,s,t)=>{Promise.resolve().then(t.bind(t,58918))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58918:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\form-submission\\\\[hashedId]\\\\sign-in\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\form-submission\\[hashedId]\\sign-in\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86429:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(60687);t(43210);let i=()=>(0,r.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,r.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,7404,1658,6560,8610,2198,5823,5841],()=>t(30844));module.exports=r})();