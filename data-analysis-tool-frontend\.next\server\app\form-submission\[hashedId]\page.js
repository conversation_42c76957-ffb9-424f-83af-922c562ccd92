(()=>{var e={};e.id=7893,e.ids=[7893],e.modules={1510:(e,r,t)=>{"use strict";t.d(r,{F0:()=>p,pe:()=>i});let{Axios:s,AxiosError:i,CanceledError:n,isCancel:a,CancelToken:o,VERSION:l,all:d,Cancel:u,isAxiosError:p,spread:c,toFormData:m,AxiosHeaders:h,HttpStatusCode:x,formToJSON:b,getAdapter:f,mergeConfig:g}=t(51060).A},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24746:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Kobo-project\\\\data-analysis-tool-frontend\\\\app\\\\form-submission\\\\[hashedId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\form-submission\\[hashedId]\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31207:(e,r,t)=>{"use strict";t.d(r,{XV:()=>i,cZ:()=>o,ru:()=>n,yi:()=>s});let s=(e,r)=>{let t=new Map;e.forEach(e=>{let s=r.filter(r=>r.questionGroupId===e.id).sort((e,r)=>e.position-r.position);t.set(e.id,{...e,subGroups:[],question:s})});let s=[];return e.forEach(e=>{let r=t.get(e.id);if(e.parentGroupId){let s=t.get(e.parentGroupId);s&&(s.subGroups=s.subGroups||[],s.subGroups.push(r))}else s.push(r)}),s},i=(e,r)=>{let t=[];return e.forEach(e=>{let r=e=>[...e.question||[],...(e.subGroups||[]).flatMap(r)],s=r(e),i=s.length>0?Math.min(...s.map(e=>e.position)):e.order;t.push({type:"group",data:e,order:i,originalPosition:i})}),r.forEach(e=>{t.push({type:"question",data:e,order:e.position,originalPosition:e.position})}),t.sort((e,r)=>e.order===r.order?(e.originalPosition||e.order)-(r.originalPosition||r.order):e.order-r.order)},n=e=>e.filter(e=>null===e.questionGroupId||void 0===e.questionGroupId),a=e=>{let r=[];return e.forEach(e=>{r.push(e.id),e.subGroups&&e.subGroups.length>0&&r.push(...a(e.subGroups))}),r},o=(e,r=!0)=>{let t={};return a(e).forEach(e=>{t[e]=r}),t}},33873:e=>{"use strict";e.exports=require("path")},44305:(e,r,t)=>{"use strict";t.d(r,{A:()=>d});var s=t(60687),i=t(43210),n=t(78272),a=t(14952),o=t(69396);let l=({group:e,nestingLevel:r=0,visibleQuestions:t,nestedQuestions:d,renderQuestionInput:u,errors:p,onToggleExpansion:c,isExpanded:m,expandedGroups:h,className:x=""})=>{let[b,f]=(0,i.useState)(!0),g=void 0!==m?m:b,y=e.question||[],v=y.filter(e=>t.some(r=>r.id===e.id)),j=(e.subGroups||[]).filter(e=>(e.question||[]).some(e=>t.some(r=>r.id===e.id)));return 0===v.length&&0===j.length?null:(0,s.jsxs)("div",{className:`border border-neutral-400 rounded-md bg-card shadow-sm mb-4 ${r>0?"ml-8 border-l-4 border-l-primary-300":""} ${x}`,children:[(0,s.jsx)("div",{className:"flex items-center justify-between p-4 bg-neutral-100 border-b border-neutral-300 rounded-t-md cursor-pointer hover:bg-neutral-200 dark:bg-gray-700 dark:border-gray-600 dark:hover:bg-gray-600",onClick:()=>{c?c(e.id):f(!b)},children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[g?(0,s.jsx)(n.A,{className:"h-5 w-5 text-neutral-700 dark:text-neutral-300"}):(0,s.jsx)(a.A,{className:"h-5 w-5 text-neutral-700 dark:text-neutral-300"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-neutral-900 dark:text-neutral-100",children:e.title}),(0,s.jsxs)("span",{className:"text-sm text-neutral-700 dark:text-neutral-400",children:["(",v.length+j.reduce((e,r)=>e+(r.question?.length||0),0)," visible question",v.length+j.reduce((e,r)=>e+(r.question?.length||0),0)!==1?"s":"",")"]})]})}),g&&(0,s.jsxs)("div",{className:"p-4 space-y-4",children:[j.sort((e,r)=>e.order-r.order).map(e=>{let i=h?h[e.id]:void 0;return(0,s.jsx)(l,{group:e,nestingLevel:r+1,visibleQuestions:t,nestedQuestions:d,renderQuestionInput:u,errors:p,onToggleExpansion:c,isExpanded:i,expandedGroups:h,className:x},e.id)}),d.filter(e=>y.some(r=>r.id===e.question.id)).map(e=>(0,s.jsx)(o.A,{questionGroup:e,renderQuestionInput:u,errors:p,className:""},e.question.id))]})]})},d=l},51401:(e,r,t)=>{Promise.resolve().then(t.bind(t,24746))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58025:(e,r,t)=>{Promise.resolve().then(t.bind(t,93044))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93044:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>k});var s=t(60687),i=t(43210),n=t(29494),a=t(54050),o=t(16189),l=t(6986),d=t(75531),u=t(96),p=t(71845),c=t(86429),m=t(39390),h=t(15616),x=t(93437),b=t(40347),f=t(70334),g=t(54864),y=t(19150),v=t(70332);t(24527);var j=t(69396),q=t(44305),N=t(31207),w=t(21650);function k(){let e=(0,g.wA)();(0,o.useRouter)();let{hashedId:r}=(0,o.useParams)(),t=(0,l.D)(r),{status:k,isAuthenticated:I,isLoading:E}=(0,w.A)(),[A,C]=(0,i.useState)({}),[P,G]=(0,i.useState)({}),[S,O]=(0,i.useState)(!1),[T,_]=(0,i.useState)([]),[M,R]=(0,i.useState)([]),[$,F]=(0,i.useState)({}),{data:D,isLoading:K,isError:Q}=(0,n.I)({queryKey:["questions",t],queryFn:()=>(0,d.K4)({projectId:t}),enabled:!!t}),{data:U=[]}=(0,n.I)({queryKey:["questionGroups",t],queryFn:()=>(0,u.pr)({projectId:t}),enabled:!!t}),{data:z}=(0,n.I)({queryKey:["project",t],queryFn:()=>(0,p.kf)({projectId:t}),enabled:!!t}),J=(0,i.useMemo)(()=>(0,N.yi)(U,D||[]),[U,D]),L=(0,i.useMemo)(()=>(0,N.ru)(D||[]),[D]),Y=(0,i.useMemo)(()=>(0,N.XV)(J,L),[J,L]),V=(0,i.useCallback)(e=>{F(r=>({...r,[e]:!r[e]}))},[]),X=(0,a.n)({mutationFn:async e=>{let r=D?.map(r=>{let s,i,n,a=e[r.id],o="selectmany"===r.inputType,l="selectone"===r.inputType;if(!o&&!l&&(null==a||""===a)||l&&(!a||""===a.trim()))return null;if(o&&Array.isArray(a)&&r.questionOptions){let e=a.map(e=>{let t=r.questionOptions.find(r=>r.label===e);return t?.id}).filter(e=>void 0!==e);s=e.length>0?e:[]}else if(l&&a&&r.questionOptions){let e=r.questionOptions.find(e=>e.label===a);if(void 0===(s=e?.id))return console.warn(`Could not find option ID for selectone question ${r.id} with value "${a}"`),null}if(null==(i=o?Array.isArray(a)?a.join(", "):"":"number"===r.inputType||"decimal"===r.inputType?a?Number(a):void 0:"date"===r.inputType||"dateandtime"===r.inputType?a||void 0:"table"===r.inputType?Array.isArray(a)&&a.length>0?JSON.stringify(a):void 0:a?String(a):void 0))return null;n=o?Array.isArray(s)?s:[]:l&&"number"==typeof s?s:void 0;let d={projectId:Number(t),questionId:r.id,answerType:String(r.inputType),value:i,isOtherOption:!1};return void 0!==n&&(d.questionOptionId=n),d}).filter(e=>null!==e)||[];if(0===r.length)throw Error("No valid answers to submit. Please fill out at least one field.");return await (0,p.lj)(r)},onSuccess:()=>{e((0,y.Ds)({message:"Form submitted successfully",type:"success"})),C({}),window.dispatchEvent(new Event("form-submitted")),localStorage.setItem("form_submitted",Date.now().toString())},onError:r=>{e((0,y.Ds)({message:"Failed to submit form. Please try again.",type:"error"})),console.error("Submission Error:",r)},onSettled:()=>{O(!1)}}),H=(0,i.useCallback)((e,r)=>{C(t=>({...t,[e]:r})),G(r=>({...r,[e]:""}))},[]),Z=()=>{let e={};return T.forEach(r=>{if(r.isRequired){let t=A[r.id];("string"==typeof t&&!t.trim()||Array.isArray(t)&&0===t.length||null==t)&&(e[r.id]=`${r.label} is required`)}}),G(e),0===Object.keys(e).length},B=async e=>{e.preventDefault(),Z()&&(O(!0),X.mutate(A))},W=e=>!!D&&D.some(r=>r.questionOptions?.some(r=>r.nextQuestionId===e)),ee=e=>e.questionOptions?.some(e=>e.nextQuestionId)||!1,er=e=>{let r=A[e.id]??("selectmany"===e.inputType?[]:"");switch(e.inputType){case"text":if(e.hint?.includes("multiline"))return(0,s.jsx)(h.T,{value:r,onChange:r=>H(e.id,r.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});return(0,s.jsx)("input",{className:"input-field w-full",value:r,onChange:r=>H(e.id,r.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});case"number":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",value:r,onChange:r=>H(e.id,r.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});case"decimal":return(0,s.jsx)("input",{className:"input-field w-full",type:"number",step:"any",value:r,onChange:r=>H(e.id,r.target.value),placeholder:e.placeholder||"Your answer",required:e.isRequired});case"selectone":return(0,s.jsx)(b.z,{value:r,onValueChange:r=>H(e.id,r),required:e.isRequired,children:(0,s.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map((e,r)=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(b.C,{value:e.label,id:`option-${e.id}`}),(0,s.jsx)(m.J,{htmlFor:`option-${e.id}`,className:"cursor-pointer",children:e.label}),e.sublabel&&(0,s.jsx)("p",{className:"text-sm text-neutral-700 ml-4",children:`(${e.sublabel})`})]},r))})});case"selectmany":return(0,s.jsx)("div",{className:"space-y-2",children:e.questionOptions?.map(t=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(x.S,{id:`option-${t.id}`,checked:(r||[]).includes(t.label),onCheckedChange:s=>{let i=r||[],n=s?[...i,t.label]:i.filter(e=>e!==t.label);H(e.id,n)}}),(0,s.jsx)(m.J,{htmlFor:`option-${t.id}`,className:"cursor-pointer",children:t.label})]},t.id))});case"date":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"date",value:r,onChange:r=>H(e.id,r.target.value),placeholder:e.placeholder||"Select date",required:e.isRequired})});case"dateandtime":return(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("input",{className:"input-field w-full",type:"time",value:r,onChange:r=>H(e.id,r.target.value),placeholder:e.placeholder||"Select time",required:e.isRequired})});case"table":return(0,s.jsx)(v.N,{questionId:e.id,value:r,onChange:r=>H(e.id,r),required:e.isRequired,tableLabel:e.label});default:return null}},et=e=>{let r=W(e.id),t=ee(e);return(0,s.jsxs)("div",{className:`border rounded-md p-4 ${r?"border-primary-200 dark:border-primary-700 bg-primary-100 dark:bg-primary-900/20":"border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"}`,children:[(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(m.J,{className:"text-base font-medium",children:[e.label,e.isRequired&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),r&&(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-200 text-primary-800 dark:bg-primary-900 dark:text-primary-200",children:[(0,s.jsx)(f.A,{className:"w-3 h-3 mr-1"}),"Follow-up"]}),t&&(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-200 text-accent-800 dark:bg-accent-700/20 dark:text-accent-200",children:"Has conditions"})]}),e.hint&&(0,s.jsx)("p",{className:`text-sm mt-1 ${r?"text-primary-700 dark:text-primary-300":"text-muted-foreground"}`,children:e.hint}),P[e.id]&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:P[e.id]})]}),(0,s.jsx)("div",{className:"mt-2",children:er(e)})]},e.id)};return E||K?(0,s.jsx)(c.A,{}):I?Q||!D?(0,s.jsx)("p",{className:"text-sm text-red-500",children:"Error loading form. Please try again."}):(0,s.jsx)("div",{className:"min-h-screen w-full bg-neutral-100 dark:bg-gray-900 flex flex-col items-center p-4 md:p-6",children:(0,s.jsxs)("div",{className:"w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("h2",{className:"text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700",children:["Form Submission ",z?.name?` for ${z.name}`:""]}),(0,s.jsx)("form",{onSubmit:B,className:"p-6",children:(0,s.jsxs)("div",{className:"space-y-6",children:[D&&0!==D.length?Y.map(e=>{if("group"===e.type){let r=e.data,t=$[r.id];return(0,s.jsx)(q.A,{group:r,nestingLevel:0,visibleQuestions:T,nestedQuestions:M,renderQuestionInput:er,errors:P,onToggleExpansion:V,isExpanded:t,expandedGroups:$,className:""},`group-${r.id}`)}{let r=e.data;if(!T.some(e=>e.id===r.id))return null;let t=M.find(e=>e.question.id===r.id);return t?(0,s.jsx)(j.A,{questionGroup:t,renderQuestionInput:er,errors:P,className:""},r.id):et(r)}}):(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}),D.length>0&&(0,s.jsx)("div",{className:"mt-6 flex justify-end",children:(0,s.jsx)("button",{className:"btn-primary",type:"submit",disabled:S,children:S?"Submitting...":"Submit Form"})}),0===D.length&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"This form has no questions yet."})}),D&&D.length>0&&0===T.length&&(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"No questions are currently visible. Please check your form configuration."})})]})})]})}):null}},94735:e=>{"use strict";e.exports=require("events")},99956:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>u,routeModule:()=>c,tree:()=>d});var s=t(65239),i=t(48088),n=t(88170),a=t.n(n),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["form-submission",{children:["[hashedId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,24746)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\form-submission\\[hashedId]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Desktop\\Kobo-project\\data-analysis-tool-frontend\\app\\form-submission\\[hashedId]\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/form-submission/[hashedId]/page",pathname:"/form-submission/[hashedId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,7404,1658,6560,3851,5841,9168,2060],()=>t(99956));module.exports=s})();