"use strict";exports.id=2060,exports.ids=[2060],exports.modules={96:(e,r,t)=>{t.d(r,{pr:()=>s});var a=t(12810);let s=async({projectId:e})=>{try{let{data:r}=await a.A.get(`/projects/form/${e}`);return r.data?.project?.questionGroup||[]}catch(r){console.error("Error fetching question groups from project endpoint:",r);try{let{data:r}=await a.A.post("/question-groups",{projectId:e});return r.data?.projectGroup||[]}catch(e){return console.error("Error in fallback fetch:",e),[]}}}},6986:(e,r,t)=>{t.d(r,{D:()=>i,l:()=>n});var a=t(53907);let s=process.env.SALT||"rushan-salt",o=new a.A(s,12),n=e=>o.encode(e),i=e=>{let r=o.decode(e)[0];return"bigint"==typeof r?r<Number.MAX_SAFE_INTEGER?Number(r):null:"number"==typeof r?r:null}},15616:(e,r,t)=>{t.d(r,{T:()=>n});var a=t(60687),s=t(43210),o=t(96241);let n=s.forwardRef(({className:e,...r},t)=>(0,a.jsx)("textarea",{className:(0,o.cn)("flex min-h-[80px] w-full rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-900 dark:placeholder:text-gray-500",e),ref:t,...r}));n.displayName="Textarea"},39390:(e,r,t)=>{t.d(r,{J:()=>i});var a=t(60687),s=t(43210),o=t(78148),n=t(96241);let i=s.forwardRef(({className:e,...r},t)=>(0,a.jsx)(o.b,{ref:t,className:(0,n.cn)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e),...r}));i.displayName=o.b.displayName},40347:(e,r,t)=>{t.d(r,{C:()=>c,z:()=>l});var a=t(60687),s=t(43210),o=t(14555),n=t(65822),i=t(96241);let l=s.forwardRef(({className:e,...r},t)=>(0,a.jsx)(o.bL,{className:(0,i.cn)("grid gap-2",e),...r,ref:t}));l.displayName=o.bL.displayName;let c=s.forwardRef(({className:e,...r},t)=>(0,a.jsx)(o.q7,{ref:t,className:(0,i.cn)("aspect-square h-4 w-4 rounded-full border text-gray-900 shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus-visible:ring-gray-300",e),...r,children:(0,a.jsx)(o.C1,{className:"flex items-center justify-center",children:(0,a.jsx)(n.A,{className:"h-2.5 w-2.5 fill-current text-current"})})}));c.displayName=o.q7.displayName},68988:(e,r,t)=>{t.d(r,{p:()=>o});var a=t(60687);t(43210);var s=t(96241);function o({className:e,type:r,...t}){return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-primary-500 focus-visible:ring-[1px]","focus-visible:outline-none","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},70334:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},71845:(e,r,t)=>{t.d(r,{D_:()=>u,Im:()=>c,Oo:()=>p,c3:()=>o,kf:()=>s,lj:()=>f,or:()=>l,pf:()=>d,vj:()=>n,wI:()=>m,xx:()=>i});var a=t(12810);let s=async({projectId:e})=>{let{data:r}=await a.A.get(`/projects/${e}`);return r.project},o=async e=>{let{data:r}=await a.A.post("/projects/from-template",e);return r},n=async()=>{try{let{data:e}=await a.A.get("/projects");return e.projects}catch(e){throw console.error("Error fetching projects:",e),e}},i=async e=>{let{data:r}=await a.A.delete(`/projects/delete/${e}`);return r},l=async e=>{try{let{data:r}=await a.A.delete("/projects/delete-multiple",{data:{projectIds:e}});return r}catch(e){throw console.error("Error deleting multiple projects:",e),e}},c=async e=>{try{let{data:r}=await a.A.patch(`/projects/change-status/${e}`,{status:"archived"});return r}catch(e){throw console.error("Error archiving project:",e),e}},d=async(e,r=!1)=>{try{let{data:r}=await a.A.patch(`/projects/change-status/${e}`,{status:"deployed"});return r}catch(e){throw console.error("Error deploying project:",e),e}},u=async e=>{try{let{data:r}=await a.A.patch("/projects/update-many-status",{projectIds:e,status:"archived"});return r}catch(e){throw console.error("Error archiving multiple projects:",e),e}},p=async e=>{try{let{data:r}=await a.A.post("/users/check-email",{email:e});return r}catch(e){throw Error("object"==typeof e.response?.data?.message?JSON.stringify(e.response?.data?.message):e.response?.data?.message||e.message||"Failed to check user")}},m=async({projectId:e,email:r,permissions:t})=>{try{let s=await p(r);if(!s||!s.success)throw Error(s?.message||"User not found");let{data:o}=await a.A.post("/project-users",{userId:s.user.id,projectId:e,permission:t});return o}catch(e){throw console.error("Error adding user to project:",e),Error("object"==typeof e.response?.data?.message?JSON.stringify(e.response?.data?.message):e.response?.data?.message||e.message||"Failed to add user")}},f=async e=>{try{let{data:r}=await a.A.post("/answers/multiple",e);return r}catch(e){throw console.error("Error creating answer submission:",e),e}}},86429:(e,r,t)=>{t.d(r,{A:()=>s});var a=t(60687);t(43210);let s=()=>(0,a.jsx)("div",{className:"w-full flex items-center justify-center",children:(0,a.jsx)("div",{className:"size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"})})},93437:(e,r,t)=>{t.d(r,{S:()=>i});var a=t(60687);t(43210);var s=t(40211),o=t(13964),n=t(96241);function i({className:e,...r}){return(0,a.jsx)(s.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...r,children:(0,a.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,a.jsx)(o.A,{className:"size-3.5"})})})}},96241:(e,r,t)=>{t.d(r,{Y:()=>n,cn:()=>o});var a=t(49384),s=t(82348);function o(...e){return(0,s.QP)((0,a.$)(e))}function n(e,r="short"){if(!e)return"";try{let t="string"==typeof e?new Date(e):e;if(isNaN(t.getTime()))return"";switch(r){case"short":return t.toLocaleDateString(void 0,{year:"numeric",month:"short",day:"numeric"});case"long":return t.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});case"full":return t.toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric",weekday:"long",hour:"2-digit",minute:"2-digit",second:"2-digit"});default:return t.toLocaleDateString()}}catch(r){return console.error("Error formatting date:",r),String(e)}}},96752:(e,r,t)=>{t.d(r,{A0:()=>n,BF:()=>i,Hj:()=>l,XI:()=>o,nA:()=>d,nd:()=>c});var a=t(60687);t(43210);var s=t(96241);function o({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",e),...r})})}function n({className:e,...r}){return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",e),...r})}function i({className:e,...r}){return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",e),...r})}function l({className:e,...r}){return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...r})}function c({className:e,...r}){return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...r})}function d({className:e,...r}){return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...r})}}};